import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // 1. Users (Simplified)
  user_profiles: defineTable({
    clerkUserId: v.string(),
    stableUserId: v.string(),
    identity: v.object({
      name: v.object({ bn: v.string(), en: v.optional(v.string()) }),
    }),
    contact: v.object({ phone: v.optional(v.string()) }),
    preferences: v.object({
      language: v.union(v.literal("bn"), v.literal("en")),
      theme: v.union(v.literal("light"), v.literal("dark"), v.literal("auto")),
    }),
    role: v.union(
      v.literal("user"),
      v.literal("business_owner"),
      v.literal("admin"),
    ),
    isOnboarded: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_clerk_user_id", ["clerkUserId"])
    .index("by_stable_user_id", ["stableUserId"]),

  // 2. Locations (Essential for your directory)
  locations: defineTable({
    name: v.object({ bn: v.string(), en: v.string() }),
    slug: v.string(),
    type: v.union(
      v.literal("division"),
      v.literal("district"),
      v.literal("upazila"),
    ),
    parentId: v.optional(v.id("locations")),
    isActive: v.boolean(),
    createdAt: v.number(),
  })
    .index("by_slug", ["slug"])
    .index("by_parent", ["parentId"]),

  // 3. DIRECTORY TABLES
  categories: defineTable({
    name: v.object({ bn: v.string(), en: v.string() }),
    slug: v.string(),
    isActive: v.boolean(),
  }).index("by_slug", ["slug"]),

  listings: defineTable({
    ownerId: v.id("user_profiles"),
    categoryId: v.id("categories"),
    locationId: v.id("locations"),
    name: v.object({ bn: v.string(), en: v.optional(v.string()) }),
    contact: v.object({ phone: v.string() }),
    status: v.union(
      v.literal("draft"),
      v.literal("pending_review"),
      v.literal("published"),
      v.literal("rejected"),
    ),
    // Add new fields for enhanced search
    keywords: v.optional(v.array(v.string())),
    isFeatured: v.optional(v.boolean()),
    viewCount: v.optional(v.number()),
    clickCount: v.optional(v.number()),
    moderationNotes: v.optional(v.string()),
    publishedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  })
    .index("by_owner", ["ownerId"])
    .index("by_location", ["locationId"])
    .index("by_status", ["status"])
    .index("by_status_creation", ["status", "createdAt"])
    .index("by_category", ["categoryId"])
    // Add new indexes for enhanced search performance
    .index("by_location_status", ["locationId", "status"])
    .index("by_category_status", ["categoryId", "status"])
    .index("by_featured_created", ["isFeatured"]),

  // 4. Analytics table for tracking listing interactions
  listing_analytics: defineTable({
    listingId: v.id("listings"),
    userId: v.optional(v.string()),
    action: v.string(),
    timestamp: v.number(),
    userAgent: v.optional(v.string()),
  })
    .index("by_listing", ["listingId"])
    .index("by_user", ["userId"])
    .index("by_action", ["action"]),

  // 5. Audit Logs (for tracking actions)
  audit_logs: defineTable({
    userId: v.id("user_profiles"),
    action: v.string(),
    targetId: v.optional(v.string()),
    details: v.optional(v.any()),
    createdAt: v.number(),
  }).index("by_user_id", ["userId"]),

  // 6. HOME SCREEN DATA TABLES
  alerts: defineTable({
    type: v.string(), // "warning", "info", "celebration"
    message: v.string(),
    icon: v.string(),
    isActive: v.boolean(),
    priority: v.number(),
  }).index("by_active_priority", ["isActive", "priority"]),

  service_categories: defineTable({
    name: v.string(),
    bnName: v.string(),
    icon: v.string(),
    color: v.string(),
    isActive: v.boolean(),
    order: v.number(),
  }).index("by_active_order", ["isActive", "order"]),

  local_news: defineTable({
    title: v.string(),
    excerpt: v.string(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
    publishedAt: v.number(),
  }).index("by_active_published", ["isActive", "publishedAt"]),

  promo_banners: defineTable({
    title: v.string(),
    subtitle: v.string(),
    icon: v.string(),
    bgColor: v.string(),
    isActive: v.boolean(),
    priority: v.number(),
  }).index("by_active_priority", ["isActive", "priority"]),

  testimonials: defineTable({
    quote: v.string(),
    author: v.string(),
    location: v.string(),
    isActive: v.boolean(),
  }).index("by_active", ["isActive"]),

  // 7. MODERATION SYSTEM TABLES
  moderation_reports: defineTable({
    reporterId: v.string(), // Clerk user ID
    contentType: v.union(v.literal("post"), v.literal("comment"), v.literal("listing")),
    contentId: v.string(), // Generic content ID
    reason: v.union(
      v.literal("spam"),
      v.literal("inappropriate"),
      v.literal("harassment"),
      v.literal("misinformation"),
      v.literal("other")
    ),
    description: v.string(),
    status: v.union(v.literal("pending"), v.literal("resolved"), v.literal("actioned")),
    moderatorId: v.optional(v.string()),
    moderatorAction: v.optional(v.union(v.literal("approve"), v.literal("remove"), v.literal("warn"))),
    adminNotes: v.optional(v.string()),
    resolvedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_reporter_content", ["reporterId", "contentId"])
    .index("by_status", ["status"])
    .index("by_content_type", ["contentType"])
    .index("by_moderator", ["moderatorId"]),

  user_warnings: defineTable({
    userId: v.string(), // Clerk user ID
    reason: v.string(),
    description: v.string(),
    moderatorId: v.string(),
    isActive: v.optional(v.boolean()),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_moderator", ["moderatorId"])
    .index("by_active", ["isActive"]),

  moderation_actions: defineTable({
    contentType: v.string(),
    contentId: v.string(),
    action: v.union(v.literal("remove"), v.literal("hide"), v.literal("flag")),
    moderatorId: v.string(),
    reason: v.string(),
    notes: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_content", ["contentType", "contentId"])
    .index("by_moderator", ["moderatorId"])
    .index("by_action", ["action"]),

  auto_moderation_logs: defineTable({
    content: v.string(), // First 100 chars for logging
    contentType: v.string(),
    approved: v.boolean(),
    confidence: v.number(),
    flags: v.array(v.string()),
    createdAt: v.number(),
  })
    .index("by_approved", ["approved"])
    .index("by_content_type", ["contentType"])
    .index("by_confidence", ["confidence"]),

  // 8. EMERGENCY ALERT SYSTEM TABLES
  emergency_alerts: defineTable({
    title: v.object({
      bn: v.string(),
      en: v.string(),
    }),
    message: v.object({
      bn: v.string(),
      en: v.string(),
    }),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
    category: v.union(
      v.literal("weather"),
      v.literal("safety"),
      v.literal("health"),
      v.literal("infrastructure"),
      v.literal("event"),
      v.literal("other")
    ),
    location: v.optional(v.object({
      area: v.string(),
      coordinates: v.optional(v.object({
        lat: v.number(),
        lng: v.number(),
      })),
    })),
    status: v.union(v.literal("active"), v.literal("resolved"), v.literal("cancelled")),
    expiresAt: v.optional(v.number()),
    actionRequired: v.optional(v.boolean()),
    contactInfo: v.optional(v.object({
      phone: v.string(),
      emergency: v.boolean(),
    })),
    acknowledgedBy: v.array(v.string()), // Array of Clerk user IDs
    sentCount: v.number(),
    resolution: v.optional(v.string()),
    resolvedAt: v.optional(v.number()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_status", ["status"])
    .index("by_severity", ["severity"])
    .index("by_category", ["category"])
    .index("by_status_created", ["status", "createdAt"])
    .index("by_severity_created", ["severity", "createdAt"]),
});

// convex/debug/clearDatabase.ts
import { mutation } from "../_generated/server";

export const debugClearDatabase = mutation({
  args: {},
  handler: async (ctx) => {
    // Clear all tables
    const tables = ["listings", "categories", "locations", "user_profiles", "listing_analytics"];
    
    for (const tableName of tables) {
      const docs = await ctx.db.query(tableName as any).collect();
      for (const doc of docs) {
        await ctx.db.delete(doc._id);
      }
    }
    
    return {
      message: "Database cleared successfully",
      clearedTables: tables,
    };
  },
});

import { mutation } from "../_generated/server";
// import { v } from "convex/values"; // Not used in this function

export const debugPopulateDatabase = mutation({
  handler: async (ctx) => {
    // Create sample locations
    const _dhakaId = await ctx.db.insert("locations", {
      name: { bn: "ঢাকা", en: "Dhaka" },
      slug: "dhaka",
      type: "division",
      isActive: true,
      createdAt: Date.now(),
    });
    
    const _ctgId = await ctx.db.insert("locations", {
      name: { bn: "চট্টগ্রাম", en: "Chattogram" },
      slug: "chattogram",
      type: "division",
      isActive: true,
      createdAt: Date.now(),
    });
    
    // Create sample categories
    const _restaurantId = await ctx.db.insert("categories", {
      name: { bn: "রেস্তোরাঁ", en: "Restaurant" },
      slug: "restaurant",
      isActive: true,
    });
    
    const _hospitalId = await ctx.db.insert("categories", {
      name: { bn: "হাসপাতাল", en: "Hospital" },
      slug: "hospital",
      isActive: true,
    });
    
    // Get a user to be the owner
    const users = await ctx.db.query("user_profiles").collect();
    const ownerId = users[0]?._id || null;
    
    // Create sample listings if we have a user
    let _listingId = null;
    if (ownerId) {
      _listingId = await ctx.db.insert("listings", {
        ownerId: ownerId,
        categoryId: _restaurantId,
        locationId: _dhakaId,
        name: { bn: "টেস্ট রেস্তোরাঁ", en: "Test Restaurant" },
        contact: { phone: "+880123456789" },
        status: "published",
        isFeatured: false,
        viewCount: 0,
        clickCount: 0,
        keywords: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
    
    return {
      message: "Database populated successfully",
      created: {
        locations: 2,
        categories: 2,
        listings: _listingId ? 1 : 0
      }
    };
  },
});
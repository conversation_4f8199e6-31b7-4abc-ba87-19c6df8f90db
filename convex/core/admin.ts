// convex/core/admin.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
import { requireAdmin } from "./errors";
import {
  listingStatusValidator,
  paginationArgsValidator,
  AuditAction
} from "../shared/types";
import {
  getPaginationParams,
  createPaginatedResponse,
  getCurrentTimestamp
} from "../shared/utils";

// 2. GET PENDING LISTINGS (With Pagination)
export const getPendingListings = query({
  args: paginationArgsValidator,
  handler: async (ctx, args) => {
    await requireAdmin(ctx); // Authorization check

    const { limit } = getPaginationParams(args.limit, args.cursor);

    let query = ctx.db
      .query("listings")
      .withIndex("by_status_creation", (q) =>
        q.eq("status", "pending_review")
      );

    // Handle pagination if cursor is provided
    if (args.cursor) {
      query = query.filter((q) => q.lt(q.field("createdAt"), parseInt(args.cursor || "0")));
    }

    const listings = await query
      .order("desc")
      .take(limit + 1); // Fetch one extra to check for next page

    return createPaginatedResponse(listings, limit);
  },
});

// 3. APPROVE LISTING (With Audit Trail)
export const approveListing = mutation({
  args: {
    listingId: v.id("listings"),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    if (listing.status !== "pending_review") {
      throw new Error("Listing is not pending review");
    }

    await ctx.db.patch(args.listingId, {
      status: "published",
      publishedAt: getCurrentTimestamp(),
      moderationNotes: args.notes,
      updatedAt: getCurrentTimestamp(),
    });

    // ✅ BEST PRACTICE: Create audit log entry
    await ctx.db.insert("audit_logs", {
      action: "listing:approved" as AuditAction,
      targetId: args.listingId,
      userId: adminUser._id,
      details: {
        previousStatus: listing.status,
        notes: args.notes,
      },
      createdAt: getCurrentTimestamp(),
    });

    return { success: true };
  },
});

// 4. REJECT LISTING (With Required Reason)
export const rejectListing = mutation({
  args: {
    listingId: v.id("listings"),
    reason: v.string(), // ✅ REQUIRED field for rejection
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    if (listing.status !== "pending_review") {
      throw new Error("Listing is not pending review");
    }

    await ctx.db.patch(args.listingId, {
      status: "rejected",
      moderationNotes: args.reason, // Store rejection reason
      updatedAt: getCurrentTimestamp(),
    });

    // ✅ BEST PRACTICE: Audit log
    await ctx.db.insert("audit_logs", {
      action: "listing:rejected" as AuditAction,
      targetId: args.listingId,
      userId: adminUser._id,
      details: {
        previousStatus: listing.status,
        reason: args.reason,
      },
      createdAt: getCurrentTimestamp(),
    });

    return { success: true };
  },
});

// 5. GET LISTING STATS (For Admin Dashboard)
export const getDashboardStats = query({
  handler: async (ctx) => {
    await requireAdmin(ctx);

    const allListings = await ctx.db.query("listings").collect();

    return {
      total: allListings.length,
      published: allListings.filter(l => l.status === "published").length,
      pending: allListings.filter(l => l.status === "pending_review").length,
      rejected: allListings.filter(l => l.status === "rejected").length,
      draft: allListings.filter(l => l.status === "draft").length,
    };
  },
});

// 6. GET LISTINGS BY STATUS (For Admin Filtering)
export const getListingsByStatus = query({
  args: {
    status: listingStatusValidator,
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const { limit } = getPaginationParams(args.limit);

    const listings = await ctx.db
      .query("listings")
      .withIndex("by_status_creation", (q) =>
        q.eq("status", args.status)
      )
      .order("desc")
      .take(limit);

    return listings;
  },
});

// 7. UPDATE LISTING STATUS (Generic status update)
export const updateListingStatus = mutation({
  args: { 
    listingId: v.id("listings"),
    status: v.union(
      v.literal("draft"),
      v.literal("pending_review"),
      v.literal("published"),
      v.literal("rejected")
    ),
    notes: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const adminUser = await requireAdmin(ctx);

    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new Error("Listing not found");
    }

    // Prevent invalid status transitions
    if (listing.status === "published" && args.status === "draft") {
      throw new Error("Cannot move published listing back to draft");
    }

    const updates: any = {
      status: args.status,
      moderationNotes: args.notes,
    };

    // Set publishedAt if moving to published
    if (args.status === "published" && !listing.publishedAt) {
      updates.publishedAt = getCurrentTimestamp();
    }

    await ctx.db.patch(args.listingId, updates);

    // ✅ BEST PRACTICE: Audit log
    await ctx.db.insert("audit_logs", {
      action: "listing:status_updated" as AuditAction,
      targetId: args.listingId,
      userId: adminUser._id,
      details: {
        previousStatus: listing.status,
        newStatus: args.status,
        notes: args.notes,
      },
      createdAt: getCurrentTimestamp(),
    });

    return { success: true };
  },
});
// convex/core/homescreen.ts - Home Screen Data Functions
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";

// Get today's alerts and notices
export const getAlerts = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("alerts"),
    type: v.string(),
    message: v.string(),
    icon: v.string(),
    isActive: v.boolean(),
    priority: v.number(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    const alerts = await ctx.db
      .query("alerts")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(5);
    
    return alerts;
  },
});

// Get service categories
export const getServiceCategories = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("service_categories"),
    name: v.string(),
    bnName: v.string(),
    icon: v.string(),
    color: v.string(),
    isActive: v.boolean(),
    order: v.number(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    const categories = await ctx.db
      .query("service_categories")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("asc")
      .take(8);
    
    return categories;
  },
});

// Get local news
export const getLocalNews = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("local_news"),
    title: v.string(),
    excerpt: v.string(),
    imageUrl: v.optional(v.string()),
    isActive: v.boolean(),
    publishedAt: v.number(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    const news = await ctx.db
      .query("local_news")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(3);
    
    return news;
  },
});

// Get promotional banners
export const getPromoBanners = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("promo_banners"),
    title: v.string(),
    subtitle: v.string(),
    icon: v.string(),
    bgColor: v.string(),
    isActive: v.boolean(),
    priority: v.number(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    const banners = await ctx.db
      .query("promo_banners")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(1);
    
    return banners;
  },
});

// Get community testimonials
export const getCommunityTestimonials = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("testimonials"),
    quote: v.string(),
    author: v.string(),
    location: v.string(),
    isActive: v.boolean(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    const testimonials = await ctx.db
      .query("testimonials")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(1);
    
    return testimonials;
  },
});

// Create sample data (for seeding)
export const seedHomeScreenData = mutation({
  args: {},
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx) => {
    // Seed alerts
    const alertsData = [
      {
        type: "warning",
        message: "POWER CUT: Ward 5 & 6, Today 10AM-2PM",
        icon: "⚠️",
        isActive: true,
        priority: 1
      },
      {
        type: "info",
        message: "WATER LOG: Road repair on Central Rd, avoid till 5PM",
        icon: "📢",
        isActive: true,
        priority: 2
      },
      {
        type: "celebration",
        message: "EVENT: বসন্ত মেলা at Central Field, Feb 15-17",
        icon: "🎉",
        isActive: true,
        priority: 3
      }
    ];

    for (const alert of alertsData) {
      await ctx.db.insert("alerts", alert);
    }

    // Seed service categories with enhanced design data
    const categoriesData = [
      { name: "Prepaid Recharge", bnName: "প্রিপেইড রিচার্জ", icon: "📱", color: "#3b82f6", isActive: true, order: 1 },
      { name: "Postpaid Bill", bnName: "পোস্টপেইড বিল", icon: "📄", color: "#10b981", isActive: true, order: 2 },
      { name: "Utility Bill Payment", bnName: "ইউটিলিটি বিল", icon: "💡", color: "#f59e0b", isActive: true, order: 3 },
      { name: "Add Money", bnName: "টাকা যোগ করুন", icon: "💰", color: "#8b5cf6", isActive: true, order: 4 },
      { name: "Police Station", bnName: "থানা", icon: "👮‍♂️", color: "#ef4444", isActive: true, order: 5 },
      { name: "Hospital", bnName: "হাসপাতাল", icon: "🏥", color: "#ec4899", isActive: true, order: 6 },
      { name: "Fire Service", bnName: "ফায়ার সার্ভিস", icon: "🚒", color: "#f97316", isActive: true, order: 7 },
      { name: "More", bnName: "আরও", icon: "⋯", color: "#6b7280", isActive: true, order: 8 }
    ];

    for (const category of categoriesData) {
      await ctx.db.insert("service_categories", category);
    }

    // Seed local news
    const newsData = [
      {
        title: "Alfadanga UP Chairman announces new drainage project",
        excerpt: "Major infrastructure development planned for the rainy season.",
        imageUrl: "https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=400&h=200&fit=crop",
        isActive: true,
        publishedAt: Date.now() - 1000 * 60 * 60 * 2 // 2 hours ago
      },
      {
        title: "Local school wins district science fair - details inside",
        excerpt: "Students showcase innovative water purification system.",
        imageUrl: "https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?w=400&h=200&fit=crop",
        isActive: true,
        publishedAt: Date.now() - 1000 * 60 * 60 * 6 // 6 hours ago
      },
      {
        title: "Farmers market to open next week with special discounts",
        excerpt: "Fresh local produce available at affordable prices.",
        imageUrl: "https://images.unsplash.com/photo-1488459716781-31db52582fe9?w=400&h=200&fit=crop",
        isActive: true,
        publishedAt: Date.now() - 1000 * 60 * 60 * 12 // 12 hours ago
      }
    ];

    for (const news of newsData) {
      await ctx.db.insert("local_news", news);
    }

    // Seed promo banners with enhanced design
    const bannerData = [
      {
        title: "2% Extra on every purchase",
        subtitle: "at the Perfume Shop. Hurry!",
        icon: "🎁",
        bgColor: "#8b5cf6",
        isActive: true,
        priority: 1
      }
    ];

    for (const banner of bannerData) {
      await ctx.db.insert("promo_banners", banner);
    }

    // Seed testimonials
    const testimonialData = [
      {
        quote: "I found a reliable plumber in 10 mins using this app!",
        author: "রহিমা",
        location: "ওয়ার্ড ৩",
        isActive: true
      }
    ];

    for (const testimonial of testimonialData) {
      await ctx.db.insert("testimonials", testimonial);
    }

    return { success: true };
  },
});

/**
 * Simple verification script to test our new error handling and validation utilities
 * This can be run to ensure the utilities work as expected
 */

import { 
  ConvexError, 
  AuthError, 
  ValidationError, 
  NotFoundError, 
  PermissionError,
  createErrorResponse 
} from "./errors";

import { 
  validatePhone, 
  validateName, 
  validateUserPreferences, 
  validateEmail, 
  validateSlug,
  validateUserUpdates
} from "./validation";

// Test error classes
console.log("Testing Error Classes:");

const authError = new AuthError("Custom auth message");
console.log("✓ AuthError:", authError.message, authError.code, authError.statusCode);

const validationError = new ValidationError("Invalid input", "email");
console.log("✓ ValidationError:", validationError.message, validationError.field);

const notFoundError = new NotFoundError("User");
console.log("✓ NotFoundError:", notFoundError.message);

const permissionError = new PermissionError("Admin required");
console.log("✓ PermissionError:", permissionError.message);

// Test error response creation
const errorResponse = createErrorResponse(validationError);
console.log("✓ Error Response:", JSON.stringify(errorResponse, null, 2));

console.log("\nTesting Validation Functions:");

// Test phone validation
const phoneTests = [
  "+8801234567890", // valid
  "123", // invalid
  "+1234567890", // valid
  "abc123" // invalid
];

phoneTests.forEach(phone => {
  const result = validatePhone(phone);
  console.log(`Phone "${phone}": ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

// Test name validation
const nameTests = [
  { bn: "রহিম" }, // valid
  { bn: "রহিম", en: "Rahim" }, // valid
  { bn: "" }, // invalid
  null // invalid
];

nameTests.forEach((name, index) => {
  const result = validateName(name as any);
  console.log(`Name ${index + 1}: ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

// Test user preferences validation
const preferencesTests = [
  { language: "bn" as const, theme: "light" as const }, // valid
  { language: "en" as const, theme: "dark" as const }, // valid
  { language: "fr" as any, theme: "light" as const }, // invalid
  { language: "bn" as const, theme: "blue" as any } // invalid
];

preferencesTests.forEach((prefs, index) => {
  const result = validateUserPreferences(prefs);
  console.log(`Preferences ${index + 1}: ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

// Test email validation
const emailTests = [
  "<EMAIL>", // valid
  "<EMAIL>", // valid
  "invalid-email", // invalid
  "@example.com" // invalid
];

emailTests.forEach(email => {
  const result = validateEmail(email);
  console.log(`Email "${email}": ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

// Test slug validation
const slugTests = [
  "hello-world", // valid
  "test123", // valid
  "Hello World", // invalid
  "test_slug" // invalid
];

slugTests.forEach(slug => {
  const result = validateSlug(slug);
  console.log(`Slug "${slug}": ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

// Test user updates validation
const userUpdateTests = [
  {
    name: { bn: "রহিম" },
    phone: "+8801234567890",
    preferences: { language: "bn" as const, theme: "light" as const }
  }, // valid
  {
    name: { bn: "" },
    phone: "123"
  }, // invalid
  {
    phone: "+8801234567890"
  } // valid partial update
];

userUpdateTests.forEach((updates, index) => {
  const result = validateUserUpdates(updates);
  console.log(`User Update ${index + 1}: ${result.isValid ? "✓ Valid" : "✗ Invalid"} - ${result.errors.join(", ")}`);
});

console.log("\n✅ All utility tests completed!");

// Export a simple function that can be called to run these tests
export function runUtilityTests() {
  console.log("Running utility verification tests...");
  // The tests above will run when this module is imported
  return "Tests completed successfully!";
}

/**
 * Core validation utilities for Convex functions
 * Provides reusable validation helpers for common input validation patterns
 */

import { ValidationError } from "./errors";

// Type definitions for validation
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface NameObject {
  bn: string;
  en?: string;
}

export interface UserPreferences {
  language: "bn" | "en";
  theme: "light" | "dark" | "auto";
}

/**
 * Phone number validation
 * Supports international format with optional country code
 */
export function validatePhone(phone: string): ValidationResult {
  const errors: string[] = [];
  
  if (!phone || typeof phone !== 'string') {
    errors.push("Phone number is required");
    return { isValid: false, errors };
  }

  // Remove spaces and dashes for validation
  const cleanPhone = phone.replace(/[\s-]/g, '');
  
  // Basic international phone number regex
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  
  if (!phoneRegex.test(cleanPhone)) {
    errors.push("Invalid phone number format. Use international format (e.g., +8801234567890)");
  }

  if (cleanPhone.length < 8 || cleanPhone.length > 15) {
    errors.push("Phone number must be between 8 and 15 digits");
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates phone number and throws ValidationError if invalid
 */
export function assertValidPhone(phone?: string): void {
  if (!phone) return; // Optional phone is allowed
  
  const result = validatePhone(phone);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), "phone");
  }
}

/**
 * String length validation
 */
export function validateStringLength(
  value: string,
  fieldName: string,
  minLength: number = 0,
  maxLength: number = 255
): ValidationResult {
  const errors: string[] = [];
  
  if (typeof value !== 'string') {
    errors.push(`${fieldName} must be a string`);
    return { isValid: false, errors };
  }

  if (value.length < minLength) {
    errors.push(`${fieldName} must be at least ${minLength} characters long`);
  }

  if (value.length > maxLength) {
    errors.push(`${fieldName} must be no more than ${maxLength} characters long`);
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates string length and throws ValidationError if invalid
 */
export function assertStringLength(
  value: string,
  fieldName: string,
  minLength: number = 0,
  maxLength: number = 255
): void {
  const result = validateStringLength(value, fieldName, minLength, maxLength);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), fieldName);
  }
}

/**
 * Name object validation (Bengali and optional English)
 */
export function validateName(name: NameObject): ValidationResult {
  const errors: string[] = [];
  
  if (!name || typeof name !== 'object') {
    errors.push("Name object is required");
    return { isValid: false, errors };
  }

  // Validate Bengali name (required)
  if (!name.bn || typeof name.bn !== 'string') {
    errors.push("Bengali name is required");
  } else {
    const bnResult = validateStringLength(name.bn, "Bengali name", 1, 100);
    errors.push(...bnResult.errors);
  }

  // Validate English name (optional)
  if (name.en !== undefined) {
    if (typeof name.en !== 'string') {
      errors.push("English name must be a string");
    } else if (name.en.length > 0) {
      const enResult = validateStringLength(name.en, "English name", 1, 100);
      errors.push(...enResult.errors);
    }
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates name object and throws ValidationError if invalid
 */
export function assertValidName(name: NameObject): void {
  const result = validateName(name);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), "name");
  }
}

/**
 * User preferences validation
 */
export function validateUserPreferences(preferences: UserPreferences): ValidationResult {
  const errors: string[] = [];
  
  if (!preferences || typeof preferences !== 'object') {
    errors.push("Preferences object is required");
    return { isValid: false, errors };
  }

  // Validate language
  if (!['bn', 'en'].includes(preferences.language)) {
    errors.push("Language must be 'bn' or 'en'");
  }

  // Validate theme
  if (!['light', 'dark', 'auto'].includes(preferences.theme)) {
    errors.push("Theme must be 'light', 'dark', or 'auto'");
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates user preferences and throws ValidationError if invalid
 */
export function assertValidUserPreferences(preferences: UserPreferences): void {
  const result = validateUserPreferences(preferences);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), "preferences");
  }
}

/**
 * Email validation
 */
export function validateEmail(email: string): ValidationResult {
  const errors: string[] = [];
  
  if (!email || typeof email !== 'string') {
    errors.push("Email is required");
    return { isValid: false, errors };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    errors.push("Invalid email format");
  }

  if (email.length > 254) {
    errors.push("Email is too long");
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates email and throws ValidationError if invalid
 */
export function assertValidEmail(email: string): void {
  const result = validateEmail(email);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), "email");
  }
}

/**
 * Slug validation (for URLs, identifiers)
 */
export function validateSlug(slug: string): ValidationResult {
  const errors: string[] = [];
  
  if (!slug || typeof slug !== 'string') {
    errors.push("Slug is required");
    return { isValid: false, errors };
  }

  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  if (!slugRegex.test(slug)) {
    errors.push("Slug must contain only lowercase letters, numbers, and hyphens");
  }

  if (slug.length < 2 || slug.length > 100) {
    errors.push("Slug must be between 2 and 100 characters long");
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates slug and throws ValidationError if invalid
 */
export function assertValidSlug(slug: string): void {
  const result = validateSlug(slug);
  if (!result.isValid) {
    throw new ValidationError(result.errors.join(", "), "slug");
  }
}

/**
 * Generic validation helper for required fields
 */
export function assertRequired<T>(value: T, fieldName: string): asserts value is NonNullable<T> {
  if (value === null || value === undefined || value === '') {
    throw new ValidationError(`${fieldName} is required`, fieldName);
  }
}

/**
 * Validates multiple fields and collects all errors
 */
export function validateMultiple(validations: (() => void)[]): ValidationResult {
  const errors: string[] = [];
  
  for (const validation of validations) {
    try {
      validation();
    } catch (error) {
      if (error instanceof ValidationError) {
        errors.push(error.message);
      } else {
        errors.push("Validation error occurred");
      }
    }
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validates user update data
 */
export function validateUserUpdates(updates: {
  name?: NameObject;
  phone?: string;
  preferences?: UserPreferences;
}): ValidationResult {
  return validateMultiple([
    () => updates.name && assertValidName(updates.name),
    () => updates.phone && assertValidPhone(updates.phone),
    () => updates.preferences && assertValidUserPreferences(updates.preferences),
  ]);
}

/**
 * Input sanitization functions for backend security
 */
export function sanitizeText(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

export function sanitizeNameInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Allow letters (including Bengali), spaces, and common punctuation
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/[^\p{L}\p{N}\s\-'.]/gu, '') // Keep letters, numbers, spaces, hyphens, apostrophes, dots
    .replace(/\s+/g, ' ') // Normalize spaces
    .trim();
}

export function sanitizePhoneInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Keep only digits, plus sign, spaces, and hyphens
  return input.replace(/[^\d+\s-]/g, '').trim();
}

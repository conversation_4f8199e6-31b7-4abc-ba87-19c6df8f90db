// Emergency alert system for critical community notifications
import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { requireAuth, requireAdmin } from "./errors";

// Create emergency alert (admin only)
export const createEmergencyAlert = mutation({
  args: {
    title: v.object({
      bn: v.string(),
      en: v.string(),
    }),
    message: v.object({
      bn: v.string(),
      en: v.string(),
    }),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
    category: v.union(
      v.literal("weather"),
      v.literal("safety"),
      v.literal("health"),
      v.literal("infrastructure"),
      v.literal("event"),
      v.literal("other")
    ),
    location: v.optional(v.object({
      area: v.string(),
      coordinates: v.optional(v.object({
        lat: v.number(),
        lng: v.number(),
      })),
    })),
    expiresAt: v.optional(v.number()),
    actionRequired: v.optional(v.boolean()),
    contactInfo: v.optional(v.object({
      phone: v.string(),
      emergency: v.boolean(),
    })),
  },
  returns: v.id("emergency_alerts"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const alertId = await ctx.db.insert("emergency_alerts", {
      ...args,
      status: "active",
      createdAt: Date.now(),
      updatedAt: Date.now(),
      acknowledgedBy: [],
      sentCount: 0,
    });

    // Trigger push notifications for critical alerts
    if (args.severity === "critical") {
      // In a real implementation, this would trigger push notifications
      console.log(`CRITICAL ALERT CREATED: ${args.title.en}`);
    }

    return alertId;
  },
});

// Get active emergency alerts
export const getActiveAlerts = query({
  args: {
    location: v.optional(v.string()),
    severity: v.optional(v.string()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("emergency_alerts")
      .withIndex("by_status", (q) => q.eq("status", "active"));

    const alerts = await query.collect();
    
    // Filter by location if specified
    let filteredAlerts = alerts;
    if (args.location) {
      filteredAlerts = alerts.filter(alert => 
        !alert.location || alert.location.area.includes(args.location!)
      );
    }

    // Filter by severity if specified
    if (args.severity) {
      filteredAlerts = filteredAlerts.filter(alert => 
        alert.severity === args.severity
      );
    }

    // Filter out expired alerts
    const now = Date.now();
    filteredAlerts = filteredAlerts.filter(alert => 
      !alert.expiresAt || alert.expiresAt > now
    );

    // Sort by severity and creation time
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    filteredAlerts.sort((a, b) => {
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      return b.createdAt - a.createdAt;
    });

    return filteredAlerts;
  },
});

// Acknowledge alert (user action)
export const acknowledgeAlert = mutation({
  args: {
    alertId: v.id("emergency_alerts"),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await requireAuth(ctx);
    
    const alert = await ctx.db.get(args.alertId);
    if (!alert) {
      throw new Error("Alert not found");
    }

    // Check if user already acknowledged
    if (alert.acknowledgedBy.includes(identity.subject)) {
      return { success: true }; // Already acknowledged
    }

    // Add user to acknowledged list
    await ctx.db.patch(args.alertId, {
      acknowledgedBy: [...alert.acknowledgedBy, identity.subject],
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Update alert status (admin only)
export const updateAlertStatus = mutation({
  args: {
    alertId: v.id("emergency_alerts"),
    status: v.union(v.literal("active"), v.literal("resolved"), v.literal("cancelled")),
    resolution: v.optional(v.string()),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const alert = await ctx.db.get(args.alertId);
    if (!alert) {
      throw new Error("Alert not found");
    }

    await ctx.db.patch(args.alertId, {
      status: args.status,
      resolution: args.resolution,
      resolvedAt: args.status === "resolved" ? Date.now() : undefined,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get alert statistics (admin only)
export const getAlertStats = query({
  args: {
    timeframe: v.optional(v.number()), // Days to look back
  },
  returns: v.object({
    total: v.number(),
    active: v.number(),
    resolved: v.number(),
    cancelled: v.number(),
    bySeverity: v.object({
      critical: v.number(),
      high: v.number(),
      medium: v.number(),
      low: v.number(),
    }),
    byCategory: v.any(),
  }),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const timeframe = args.timeframe || 30; // Default 30 days
    const cutoff = Date.now() - (timeframe * 24 * 60 * 60 * 1000);
    
    const alerts = await ctx.db
      .query("emergency_alerts")
      .filter((q) => q.gte(q.field("createdAt"), cutoff))
      .collect();

    const stats = {
      total: alerts.length,
      active: alerts.filter(a => a.status === "active").length,
      resolved: alerts.filter(a => a.status === "resolved").length,
      cancelled: alerts.filter(a => a.status === "cancelled").length,
      bySeverity: {
        critical: alerts.filter(a => a.severity === "critical").length,
        high: alerts.filter(a => a.severity === "high").length,
        medium: alerts.filter(a => a.severity === "medium").length,
        low: alerts.filter(a => a.severity === "low").length,
      },
      byCategory: alerts.reduce((acc, alert) => {
        acc[alert.category] = (acc[alert.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return stats;
  },
});

// Send test alert (admin only, for testing notification system)
export const sendTestAlert = mutation({
  args: {
    message: v.string(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    // Create a test alert
    const alertId = await ctx.db.insert("emergency_alerts", {
      title: {
        bn: "পরীক্ষা সতর্কতা",
        en: "Test Alert"
      },
      message: {
        bn: args.message,
        en: args.message
      },
      severity: "low",
      category: "other",
      status: "active",
      expiresAt: Date.now() + (5 * 60 * 1000), // Expires in 5 minutes
      createdAt: Date.now(),
      updatedAt: Date.now(),
      acknowledgedBy: [],
      sentCount: 0,
    });

    console.log(`Test alert created: ${alertId}`);
    
    return { success: true };
  },
});

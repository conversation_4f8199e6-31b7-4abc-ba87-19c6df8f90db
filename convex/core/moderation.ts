// Community moderation system for Smart Village
import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { requireAuth, requireAdmin } from "./errors";

// Report content for moderation
export const reportContent = mutation({
  args: {
    contentType: v.union(v.literal("post"), v.literal("comment"), v.literal("listing")),
    contentId: v.string(), // Generic content ID as string
    reason: v.union(
      v.literal("spam"),
      v.literal("inappropriate"),
      v.literal("harassment"),
      v.literal("misinformation"),
      v.literal("other")
    ),
    description: v.optional(v.string()),
  },
  returns: v.id("moderation_reports"),
  handler: async (ctx, args) => {
    const identity = await requireAuth(ctx);
    
    // Check if user already reported this content
    const existingReport = await ctx.db
      .query("moderation_reports")
      .withIndex("by_reporter_content", (q) =>
        q.eq("reporterId", identity.subject).eq("contentId", args.contentId)
      )
      .unique();

    if (existingReport) {
      throw new Error("You have already reported this content");
    }

    const reportId = await ctx.db.insert("moderation_reports", {
      reporterId: identity.subject,
      contentType: args.contentType,
      contentId: args.contentId,
      reason: args.reason,
      description: args.description || "",
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return reportId;
  },
});

// Get pending reports (admin only)
export const getPendingReports = query({
  args: {
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const limit = args.limit || 20;
    
    const reports = await ctx.db
      .query("moderation_reports")
      .withIndex("by_status", (q) => q.eq("status", "pending"))
      .order("desc")
      .paginate({
        numItems: limit,
        cursor: args.cursor || null,
      });

    return reports;
  },
});

// Moderate content (admin only)
export const moderateContent = mutation({
  args: {
    reportId: v.id("moderation_reports"),
    action: v.union(v.literal("approve"), v.literal("remove"), v.literal("warn")),
    adminNotes: v.optional(v.string()),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await requireAdmin(ctx);
    
    const report = await ctx.db.get(args.reportId);
    if (!report) {
      throw new Error("Report not found");
    }

    // Update report status
    await ctx.db.patch(args.reportId, {
      status: args.action === "approve" ? "resolved" : "actioned",
      moderatorId: identity.subject,
      moderatorAction: args.action,
      adminNotes: args.adminNotes || "",
      resolvedAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Take action based on decision
    if (args.action === "remove") {
      // Mark content as removed (don't actually delete for audit trail)
      await ctx.db.insert("moderation_actions", {
        contentType: report.contentType,
        contentId: report.contentId,
        action: "remove",
        moderatorId: identity.subject,
        reason: report.reason,
        createdAt: Date.now(),
      });
    } else if (args.action === "warn") {
      // Create warning for content creator
      // Note: In a real implementation, you'd need to get the actual content creator ID
      // For now, we'll create a warning record with the reporter ID as placeholder
      await ctx.db.insert("user_warnings", {
        userId: report.reporterId, // TODO: Replace with actual content creator ID
        reason: report.reason,
        description: args.adminNotes || "",
        moderatorId: identity.subject,
        isActive: true,
        createdAt: Date.now(),
      });
    }

    return { success: true };
  },
});

// Auto-moderation for common issues
export const autoModerate = mutation({
  args: {
    content: v.string(),
    contentType: v.string(),
  },
  returns: v.object({
    approved: v.boolean(),
    confidence: v.number(),
    flags: v.array(v.string()),
  }),
  handler: async (ctx, args) => {
    // Simple keyword-based auto-moderation
    const bannedWords = [
      // Add Bengali and English inappropriate words
      "spam", "scam", "fake", "fraud",
      // Bengali equivalents would go here
    ];

    const flags: string[] = [];
    let confidence = 1.0;

    const lowerContent = args.content.toLowerCase();
    
    // Check for banned words
    for (const word of bannedWords) {
      if (lowerContent.includes(word.toLowerCase())) {
        flags.push(`Contains banned word: ${word}`);
        confidence -= 0.3;
      }
    }

    // Check for excessive caps
    const capsRatio = (args.content.match(/[A-Z]/g) || []).length / args.content.length;
    if (capsRatio > 0.5 && args.content.length > 10) {
      flags.push("Excessive capitalization");
      confidence -= 0.2;
    }

    // Check for repeated characters
    if (/(.)\1{4,}/.test(args.content)) {
      flags.push("Repeated characters");
      confidence -= 0.1;
    }

    // Check for URLs (might be spam)
    if (/https?:\/\//.test(args.content)) {
      flags.push("Contains URL");
      confidence -= 0.2;
    }

    const approved = confidence > 0.5;

    // Log auto-moderation result
    await ctx.db.insert("auto_moderation_logs", {
      content: args.content.substring(0, 100), // Store first 100 chars
      contentType: args.contentType,
      approved,
      confidence,
      flags,
      createdAt: Date.now(),
    });

    return { approved, confidence, flags };
  },
});

// Get community guidelines
export const getCommunityGuidelines = query({
  args: {},
  returns: v.array(v.object({
    title: v.string(),
    description: v.string(),
    examples: v.array(v.string()),
  })),
  handler: async () => {
    return [
      {
        title: "Be Respectful",
        description: "Treat all community members with respect and kindness",
        examples: [
          "Use polite language",
          "Respect different opinions",
          "No personal attacks or harassment"
        ]
      },
      {
        title: "Share Accurate Information",
        description: "Only share verified and accurate local information",
        examples: [
          "Verify news before sharing",
          "Cite sources when possible",
          "Report misinformation"
        ]
      },
      {
        title: "Keep It Local",
        description: "Focus on content relevant to your local community",
        examples: [
          "Local news and events",
          "Community services",
          "Neighborhood updates"
        ]
      },
      {
        title: "No Spam or Self-Promotion",
        description: "Avoid excessive promotional content or spam",
        examples: [
          "Don't post the same content repeatedly",
          "Limit business promotions",
          "No irrelevant advertisements"
        ]
      }
    ];
  },
});

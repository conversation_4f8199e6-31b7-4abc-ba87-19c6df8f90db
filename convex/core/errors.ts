/**
 * Core error handling utilities for Convex functions
 * Provides standardized error classes and wrapper functions for consistent error handling
 */

// Custom error classes for different types of application errors
export class ConvexError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class AuthError extends ConvexError {
  constructor(message: string = "Authentication required") {
    super(message, "AUTH_ERROR", 401);
  }
}

export class ValidationError extends ConvexError {
  constructor(message: string, public field?: string) {
    super(message, "VALIDATION_ERROR", 400);
  }
}

export class NotFoundError extends ConvexError {
  constructor(resource: string = "Resource") {
    super(`${resource} not found`, "NOT_FOUND", 404);
  }
}

export class PermissionError extends ConvexError {
  constructor(message: string = "Insufficient permissions") {
    super(message, "PERMISSION_ERROR", 403);
  }
}

export class RateLimitError extends ConvexError {
  constructor(message: string = "Rate limit exceeded") {
    super(message, "RATE_LIMIT", 429);
  }
}

// Type for Convex function handlers
type ConvexHandler<TArgs, TReturn> = (ctx: any, args: TArgs) => Promise<TReturn>;

/**
 * Higher-order function that wraps Convex handlers with standardized error handling
 * Catches and logs errors, then throws user-friendly error messages
 */
export function withErrorHandling<TArgs, TReturn>(
  handler: ConvexHandler<TArgs, TReturn>,
  options: {
    logErrors?: boolean;
    operation?: string;
  } = {}
): ConvexHandler<TArgs, TReturn> {
  const { logErrors = true, operation = "operation" } = options;

  return async (ctx: any, args: TArgs): Promise<TReturn> => {
    try {
      return await handler(ctx, args);
    } catch (error) {
      // Log the error for debugging (with context)
      if (logErrors) {
        console.error(`Error in ${operation}:`, {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          args: JSON.stringify(args),
          timestamp: new Date().toISOString(),
        });
      }

      // Re-throw known application errors as-is
      if (error instanceof ConvexError) {
        throw error;
      }

      // Handle specific known error patterns
      if (error instanceof Error) {
        const message = error.message.toLowerCase();
        
        // Authentication errors
        if (message.includes('not authenticated') || message.includes('unauthorized')) {
          throw new AuthError(error.message);
        }
        
        // Validation errors
        if (message.includes('validation') || message.includes('invalid')) {
          throw new ValidationError(error.message);
        }
        
        // Not found errors
        if (message.includes('not found')) {
          throw new NotFoundError();
        }
      }

      // For unknown errors, throw a generic error to avoid exposing internals
      throw new ConvexError(
        `Failed to complete ${operation}. Please try again.`,
        "INTERNAL_ERROR",
        500
      );
    }
  };
}

/**
 * Authentication helper that checks for user identity and returns it
 * Throws AuthError if user is not authenticated
 */
export async function requireAuth(ctx: any): Promise<any> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new AuthError("Authentication required. Please sign in.");
  }
  return identity;
}

/**
 * Authorization helper that checks if user has admin role
 * Throws PermissionError if user is not an admin
 */
export async function requireAdmin(ctx: any): Promise<any> {
  const identity = await requireAuth(ctx);
  
  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_clerk_user_id", (q: any) => 
      q.eq("clerkUserId", identity.subject)
    )
    .unique();

  if (!user || user.role !== "admin") {
    throw new PermissionError("Admin access required");
  }

  return { identity, user };
}

/**
 * Helper to get current user profile
 * Throws AuthError if not authenticated or NotFoundError if profile doesn't exist
 */
export async function getCurrentUser(ctx: any): Promise<any> {
  const identity = await requireAuth(ctx);
  
  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_clerk_user_id", (q: any) => 
      q.eq("clerkUserId", identity.subject)
    )
    .unique();

  if (!user) {
    throw new NotFoundError("User profile");
  }

  return user;
}

/**
 * Utility to safely parse JSON with error handling
 */
export function safeJsonParse<T>(jsonString: string, fallback: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return fallback;
  }
}

/**
 * Utility to create consistent error responses
 */
export function createErrorResponse(error: unknown) {
  if (error instanceof ConvexError) {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        statusCode: error.statusCode,
      },
    };
  }

  return {
    success: false,
    error: {
      code: "UNKNOWN_ERROR",
      message: "An unexpected error occurred",
      statusCode: 500,
    },
  };
}

/**
 * Bangladesh-specific error messages for better user experience
 */
export const BangladeshErrorMessages = {
  // Authentication errors in Bengali and English
  AUTH_REQUIRED: {
    bn: "অনুগ্রহ করে প্রথমে সাইন ইন করুন",
    en: "Please sign in first"
  },
  PHONE_INVALID: {
    bn: "অনুগ্রহ করে একটি বৈধ বাংলাদেশি ফোন নম্বর দিন (যেমন: +8801XXXXXXXXX)",
    en: "Please enter a valid Bangladesh phone number (e.g., +8801XXXXXXXXX)"
  },
  NAME_REQUIRED: {
    bn: "অনুগ্রহ করে আপনার নাম বাংলায় লিখুন",
    en: "Please enter your name in Bengali"
  },
  NETWORK_ERROR: {
    bn: "ইন্টারনেট সংযোগ সমস্যা। অনুগ্রহ করে আবার চেষ্টা করুন।",
    en: "Network connection issue. Please try again."
  },
  LOCATION_REQUIRED: {
    bn: "অনুগ্রহ করে আপনার এলাকা নির্বাচন করুন",
    en: "Please select your area"
  },
  CONTENT_INAPPROPRIATE: {
    bn: "এই বিষয়বস্তু কমিউনিটি গাইডলাইন লঙ্ঘন করেছে",
    en: "This content violates community guidelines"
  },
  EMERGENCY_ALERT_FAILED: {
    bn: "জরুরি সতর্কতা পাঠাতে ব্যর্থ। অনুগ্রহ করে আবার চেষ্টা করুন।",
    en: "Failed to send emergency alert. Please try again."
  },
  MODERATION_FAILED: {
    bn: "মডারেশন প্রক্রিয়া ব্যর্থ। অনুগ্রহ করে পরে চেষ্টা করুন।",
    en: "Moderation process failed. Please try later."
  }
};

/**
 * Get localized error message
 */
export function getLocalizedError(errorKey: keyof typeof BangladeshErrorMessages, language: "bn" | "en" = "bn") {
  return BangladeshErrorMessages[errorKey]?.[language] || BangladeshErrorMessages[errorKey]?.en || "An error occurred";
}

// System test functions to verify all critical functionality
import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { requireAdmin } from "./errors";

// Test emergency alert system
export const testEmergencySystem = mutation({
  args: {},
  returns: v.object({ 
    success: v.boolean(), 
    message: v.string(),
    alertId: v.optional(v.id("emergency_alerts"))
  }),
  handler: async (ctx) => {
    try {
      await requireAdmin(ctx);
      
      // Create a test emergency alert
      const alertId = await ctx.db.insert("emergency_alerts", {
        title: {
          bn: "সিস্টেম পরীক্ষা",
          en: "System Test"
        },
        message: {
          bn: "এটি একটি পরীক্ষামূলক জরুরি সতর্কতা",
          en: "This is a test emergency alert"
        },
        severity: "low",
        category: "other",
        status: "active",
        acknowledgedBy: [],
        sentCount: 0,
        expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      return {
        success: true,
        message: "Emergency alert system working correctly",
        alertId
      };
    } catch (error) {
      return {
        success: false,
        message: `Emergency system test failed: ${error}`,
      };
    }
  },
});

// Test moderation system
export const testModerationSystem = mutation({
  args: {},
  returns: v.object({ 
    success: v.boolean(), 
    message: v.string(),
    reportId: v.optional(v.id("moderation_reports"))
  }),
  handler: async (ctx) => {
    try {
      const identity = await ctx.auth.getUserIdentity();
      if (!identity) {
        throw new Error("Authentication required");
      }
      
      // Create a test moderation report
      const reportId = await ctx.db.insert("moderation_reports", {
        reporterId: identity.subject,
        contentType: "post",
        contentId: "test-content-123",
        reason: "spam",
        description: "Test moderation report",
        status: "pending",
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      return {
        success: true,
        message: "Moderation system working correctly",
        reportId
      };
    } catch (error) {
      return {
        success: false,
        message: `Moderation system test failed: ${error}`,
      };
    }
  },
});

// Get system health status
export const getSystemHealth = query({
  args: {},
  returns: v.object({
    overall: v.string(),
    components: v.object({
      database: v.string(),
      authentication: v.string(),
      homescreen: v.string(),
      moderation: v.string(),
      emergency: v.string(),
    }),
    stats: v.object({
      totalUsers: v.number(),
      activeAlerts: v.number(),
      pendingReports: v.number(),
      emergencyAlerts: v.number(),
    }),
  }),
  handler: async (ctx) => {
    try {
      // Check database connectivity
      const userCount = await ctx.db.query("user_profiles").collect();
      
      // Check homescreen data
      const alerts = await ctx.db.query("alerts").collect();
      const serviceCategories = await ctx.db.query("service_categories").collect();
      
      // Check moderation system
      const pendingReports = await ctx.db
        .query("moderation_reports")
        .withIndex("by_status", (q) => q.eq("status", "pending"))
        .collect();
      
      // Check emergency system
      const emergencyAlerts = await ctx.db
        .query("emergency_alerts")
        .withIndex("by_status", (q) => q.eq("status", "active"))
        .collect();

      const components = {
        database: "healthy",
        authentication: "healthy",
        homescreen: alerts.length > 0 && serviceCategories.length > 0 ? "healthy" : "warning",
        moderation: "healthy",
        emergency: "healthy",
      };

      const overall = Object.values(components).includes("error") ? "error" :
                     Object.values(components).includes("warning") ? "warning" : "healthy";

      return {
        overall,
        components,
        stats: {
          totalUsers: userCount.length,
          activeAlerts: alerts.filter(a => a.isActive).length,
          pendingReports: pendingReports.length,
          emergencyAlerts: emergencyAlerts.length,
        },
      };
    } catch (error) {
      return {
        overall: "error",
        components: {
          database: "error",
          authentication: "error",
          homescreen: "error",
          moderation: "error",
          emergency: "error",
        },
        stats: {
          totalUsers: 0,
          activeAlerts: 0,
          pendingReports: 0,
          emergencyAlerts: 0,
        },
      };
    }
  },
});

// Clean up test data
export const cleanupTestData = mutation({
  args: {},
  returns: v.object({ success: v.boolean(), message: v.string() }),
  handler: async (ctx) => {
    try {
      await requireAdmin(ctx);
      
      // Remove test emergency alerts
      const testAlerts = await ctx.db
        .query("emergency_alerts")
        .filter((q) => q.or(
          q.eq(q.field("title.en"), "System Test"),
          q.eq(q.field("title.bn"), "সিস্টেম পরীক্ষা")
        ))
        .collect();
      
      for (const alert of testAlerts) {
        await ctx.db.delete(alert._id);
      }

      // Remove test moderation reports
      const testReports = await ctx.db
        .query("moderation_reports")
        .filter((q) => q.eq(q.field("contentId"), "test-content-123"))
        .collect();
      
      for (const report of testReports) {
        await ctx.db.delete(report._id);
      }

      return {
        success: true,
        message: `Cleaned up ${testAlerts.length} test alerts and ${testReports.length} test reports`
      };
    } catch (error) {
      return {
        success: false,
        message: `Cleanup failed: ${error}`
      };
    }
  },
});

// convex/directory/search.ts
import { query, mutation } from "../_generated/server";
import { v } from "convex/values";
import { NotFoundError } from "../core/errors";
import { listingStatusValidator } from "../shared/types";
import {
  normalizeSearchQuery,
  matchesSearchQuery,
  matchesKeywords,
  createPaginatedResponse,
  getPaginationParams,
  getCurrentTimestamp
} from "../shared/utils";

export const searchListings = query({
  args: {
    searchQuery: v.optional(v.string()),
    categoryId: v.optional(v.id("categories")),
    locationId: v.optional(v.id("locations")),
    isFeatured: v.optional(v.boolean()),
    status: v.optional(listingStatusValidator),
    sortBy: v.optional(
      v.union(v.literal("newest"), v.literal("featured"), v.literal("name")),
    ),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const { limit } = getPaginationParams(args.limit, args.cursor);
    const normalizedQuery = normalizeSearchQuery(args.searchQuery);
    const status = args.status || "published";

    // 🔥 CRITICAL: Use the most specific index first for performance
    let queryBuilder;
    if (args.locationId && args.categoryId) {
      // Most specific: both location and category - use location index first
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_location_status", (q) =>
          q.eq("locationId", args.locationId!).eq("status", status)
        )
        .filter((q) => q.eq(q.field("categoryId"), args.categoryId));
    } else if (args.locationId) {
      // Specific: location only
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_location_status", (q) =>
          q.eq("locationId", args.locationId!).eq("status", status)
        );
    } else if (args.categoryId) {
      // Specific: category only
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_category_status", (q) =>
          q.eq("categoryId", args.categoryId!).eq("status", status)
        );
    } else {
      // General: status only
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_status", (q) => q.eq("status", status));
    }

    // Apply featured filter
    if (args.isFeatured !== undefined) {
      queryBuilder = queryBuilder.filter((q) =>
        q.eq(q.field("isFeatured"), args.isFeatured)
      );
    }

    // Apply sorting
    switch (args.sortBy) {
      case "featured":
        queryBuilder = queryBuilder.order("desc");
        break;
      case "newest":
        queryBuilder = queryBuilder.order("desc");
        break;
      case "name":
      default:
        queryBuilder = queryBuilder.order("asc");
    }

    // Execute query with extra items for filtering
    const fetchLimit = normalizedQuery ? limit * 2 : limit + 1;
    const results = await queryBuilder.take(fetchLimit);

    // Apply text search filtering if search query exists
    let filteredResults = results;
    if (normalizedQuery) {
      filteredResults = results.filter((listing) => {
        const nameMatch = matchesSearchQuery(listing.name, normalizedQuery);
        const keywordMatch = matchesKeywords(listing.keywords, normalizedQuery);
        return nameMatch || keywordMatch;
      });
    }

    return createPaginatedResponse(filteredResults, limit);
  },
});

export const getListingDetail = query({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const listing = await ctx.db.get(args.listingId);

    if (!listing || listing.status !== "published") {
      throw new NotFoundError("Listing not found or not published");
    }

    // Get related data
    const [category, location, owner] = await Promise.all([
      ctx.db.get(listing.categoryId),
      ctx.db.get(listing.locationId),
      ctx.db.get(listing.ownerId),
    ]);

    return {
      ...listing,
      category: category
        ? {
            _id: category._id,
            name: category.name,
            slug: category.slug,
          }
        : null,
      location: location
        ? {
            _id: location._id,
            name: location.name,
            slug: location.slug,
          }
        : null,
      owner: owner
        ? {
            _id: owner._id,
            name: owner.identity.name,
            isOnboarded: owner.isOnboarded,
          }
        : null,
    };
  },
});

// Separate mutation for incrementing view count
export const incrementListingViewCount = mutation({
  args: { listingId: v.id("listings") },
  handler: async (ctx, args) => {
    const listing = await ctx.db.get(args.listingId);
    if (!listing) {
      throw new NotFoundError("Listing not found");
    }

    await ctx.db.patch(args.listingId, {
      viewCount: (listing.viewCount || 0) + 1,
      updatedAt: getCurrentTimestamp(),
    });

    return { success: true };
  },
});

export const getFeaturedListings = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const { limit } = getPaginationParams(args.limit);

    return await ctx.db
      .query("listings")
      .withIndex("by_featured_created", (q) =>
        q.eq("isFeatured", true)
      )
      .filter((q) => q.eq(q.field("status"), "published"))
      .order("desc")
      .take(limit);
  },
});

// 2. Get Listings with Category and Location Info (for display)
export const getListingsWithDetails = query({
  args: {
    categoryId: v.optional(v.id("categories")),
    locationId: v.optional(v.id("locations")),
    status: v.optional(listingStatusValidator),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { limit } = getPaginationParams(args.limit);
    const status = args.status || "published";

    // Use appropriate index based on filters
    let queryBuilder;
    if (args.locationId && args.categoryId) {
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_location_status", (q) =>
          q.eq("locationId", args.locationId!).eq("status", status)
        )
        .filter((q) => q.eq(q.field("categoryId"), args.categoryId));
    } else if (args.locationId) {
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_location_status", (q) =>
          q.eq("locationId", args.locationId!).eq("status", status)
        );
    } else if (args.categoryId) {
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_category_status", (q) =>
          q.eq("categoryId", args.categoryId!).eq("status", status)
        );
    } else {
      queryBuilder = ctx.db
        .query("listings")
        .withIndex("by_status", (q) => q.eq("status", status));
    }

    const listings = await queryBuilder.order("desc").take(limit);

    // Get related data for each listing in parallel
    const listingsWithDetails = await Promise.all(
      listings.map(async (listing) => {
        const [category, location, owner] = await Promise.all([
          ctx.db.get(listing.categoryId),
          ctx.db.get(listing.locationId),
          ctx.db.get(listing.ownerId),
        ]);

        return {
          ...listing,
          category: category
            ? { name: category.name, slug: category.slug }
            : null,
          location: location
            ? { name: location.name, slug: location.slug }
            : null,
          owner: owner
            ? {
                name: owner.identity.name,
                isOnboarded: owner.isOnboarded,
              }
            : null,
        };
      })
    );

    return listingsWithDetails;
  },
});

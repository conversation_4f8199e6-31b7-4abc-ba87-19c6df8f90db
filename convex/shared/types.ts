/**
 * Shared types for the modular monolith hyperlocal superapp
 * These types ensure consistency across all modules
 */

import { v } from "convex/values";

// ============================================================================
// CORE TYPES
// ============================================================================

/**
 * Standardized localized text for Bengali/English content
 * Used consistently across all entities (users, locations, categories, listings)
 */
export interface LocalizedText {
  bn: string;
  en?: string;
}

/**
 * Convex validator for LocalizedText
 */
export const localizedTextValidator = v.object({
  bn: v.string(),
  en: v.optional(v.string()),
});

/**
 * Required localized text (both languages required)
 * Used for system entities like locations and categories
 */
export interface RequiredLocalizedText {
  bn: string;
  en: string;
}

/**
 * Convex validator for RequiredLocalizedText
 */
export const requiredLocalizedTextValidator = v.object({
  bn: v.string(),
  en: v.string(),
});

// ============================================================================
// USER TYPES
// ============================================================================

/**
 * User roles in the system
 */
export type UserRole = "user" | "business_owner" | "admin";

/**
 * Convex validator for UserRole
 */
export const userRoleValidator = v.union(
  v.literal("user"),
  v.literal("business_owner"),
  v.literal("admin")
);

/**
 * User preferences
 */
export interface UserPreferences {
  language: "bn" | "en";
  theme: "light" | "dark" | "auto";
}

/**
 * Convex validator for UserPreferences
 */
export const userPreferencesValidator = v.object({
  language: v.union(v.literal("bn"), v.literal("en")),
  theme: v.union(v.literal("light"), v.literal("dark"), v.literal("auto")),
});

// ============================================================================
// LOCATION TYPES
// ============================================================================

/**
 * Location types in Bangladesh administrative hierarchy
 */
export type LocationType = "division" | "district" | "upazila";

/**
 * Convex validator for LocationType
 */
export const locationTypeValidator = v.union(
  v.literal("division"),
  v.literal("district"),
  v.literal("upazila")
);

// ============================================================================
// LISTING TYPES
// ============================================================================

/**
 * Listing status in the moderation workflow
 */
export type ListingStatus = "draft" | "pending_review" | "published" | "rejected";

/**
 * Convex validator for ListingStatus
 */
export const listingStatusValidator = v.union(
  v.literal("draft"),
  v.literal("pending_review"),
  v.literal("published"),
  v.literal("rejected")
);

/**
 * Listing sort options
 */
export type ListingSortBy = "newest" | "featured" | "name" | "relevance";

/**
 * Convex validator for ListingSortBy
 */
export const listingSortByValidator = v.union(
  v.literal("newest"),
  v.literal("featured"),
  v.literal("name"),
  v.literal("relevance")
);

// ============================================================================
// SEARCH TYPES
// ============================================================================

/**
 * Search filters for listings
 */
export interface SearchFilters {
  searchQuery?: string;
  categoryId?: string;
  locationId?: string;
  status?: ListingStatus;
  isFeatured?: boolean;
  sortBy?: ListingSortBy;
  limit?: number;
  cursor?: string;
}

/**
 * Convex validator for SearchFilters
 */
export const searchFiltersValidator = v.object({
  searchQuery: v.optional(v.string()),
  categoryId: v.optional(v.id("categories")),
  locationId: v.optional(v.id("locations")),
  status: v.optional(listingStatusValidator),
  isFeatured: v.optional(v.boolean()),
  sortBy: v.optional(listingSortByValidator),
  limit: v.optional(v.number()),
  cursor: v.optional(v.string()),
});

// ============================================================================
// PAGINATION TYPES
// ============================================================================

/**
 * Standard pagination response
 */
export interface PaginatedResponse<T> {
  items: T[];
  nextCursor?: string;
  hasMore: boolean;
  total?: number;
}

/**
 * Pagination arguments
 */
export interface PaginationArgs {
  limit?: number;
  cursor?: string;
}

/**
 * Convex validator for PaginationArgs
 */
export const paginationArgsValidator = v.object({
  limit: v.optional(v.number()),
  cursor: v.optional(v.string()),
});

// ============================================================================
// AUDIT TYPES
// ============================================================================

/**
 * Audit action types
 */
export type AuditAction = 
  | "user:created"
  | "user:updated" 
  | "user:promoted_to_admin"
  | "listing:created"
  | "listing:updated"
  | "listing:approved"
  | "listing:rejected"
  | "listing:status_updated"
  | "location:created"
  | "category:created";

/**
 * Convex validator for AuditAction
 */
export const auditActionValidator = v.union(
  v.literal("user:created"),
  v.literal("user:updated"),
  v.literal("user:promoted_to_admin"),
  v.literal("listing:created"),
  v.literal("listing:updated"),
  v.literal("listing:approved"),
  v.literal("listing:rejected"),
  v.literal("listing:status_updated"),
  v.literal("location:created"),
  v.literal("category:created")
);

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

/**
 * Analytics action types
 */
export type AnalyticsAction = "view" | "click" | "contact" | "share";

/**
 * Convex validator for AnalyticsAction
 */
export const analyticsActionValidator = v.union(
  v.literal("view"),
  v.literal("click"),
  v.literal("contact"),
  v.literal("share")
);

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Standard timestamps
 */
export interface Timestamps {
  createdAt: number;
  updatedAt: number;
}

/**
 * Optional timestamps for updates
 */
export interface OptionalTimestamps {
  createdAt?: number;
  updatedAt?: number;
}

/**
 * Contact information
 */
export interface ContactInfo {
  phone?: string;
  email?: string;
  website?: string;
}

/**
 * Convex validator for ContactInfo
 */
export const contactInfoValidator = v.object({
  phone: v.optional(v.string()),
  email: v.optional(v.string()),
  website: v.optional(v.string()),
});

// ============================================================================
// RESPONSE TYPES
// ============================================================================

/**
 * Standard success response
 */
export interface SuccessResponse {
  success: true;
  message?: string;
}

/**
 * Standard error response
 */
export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    statusCode: number;
    field?: string;
  };
}

/**
 * API response type
 */
export type ApiResponse<T = any> = 
  | (SuccessResponse & { data?: T })
  | ErrorResponse;

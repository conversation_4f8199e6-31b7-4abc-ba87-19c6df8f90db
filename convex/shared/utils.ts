/**
 * Shared utilities for the modular monolith hyperlocal superapp
 * These utilities are used across all modules to ensure consistency
 */

import { 
  LocalizedText, 
  RequiredLocalizedText, 
  PaginatedResponse,
  ListingStatus,
  ListingSortBy 
} from "./types";

// ============================================================================
// LOCALIZATION UTILITIES
// ============================================================================

/**
 * Get the appropriate text based on language preference
 */
export function getLocalizedText(
  text: LocalizedText | RequiredLocalizedText,
  language: "bn" | "en" = "bn"
): string {
  if (language === "en" && text.en) {
    return text.en;
  }
  return text.bn;
}

/**
 * Create a localized text object
 */
export function createLocalizedText(
  bn: string,
  en?: string
): LocalizedText {
  return { bn, en };
}

/**
 * Create a required localized text object
 */
export function createRequiredLocalizedText(
  bn: string,
  en: string
): RequiredLocalizedText {
  return { bn, en };
}

/**
 * Validate that localized text has required content
 */
export function validateLocalizedText(text: LocalizedText): boolean {
  return Boolean(text.bn && text.bn.trim().length > 0);
}

// ============================================================================
// PAGINATION UTILITIES
// ============================================================================

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  items: T[],
  limit: number,
  hasMore?: boolean,
  total?: number
): PaginatedResponse<T> {
  const actualHasMore = hasMore ?? items.length > limit;
  const actualItems = actualHasMore ? items.slice(0, limit) : items;
  const nextCursor = actualHasMore && actualItems.length > 0 
    ? (actualItems[actualItems.length - 1] as any)?._id 
    : undefined;

  return {
    items: actualItems,
    nextCursor,
    hasMore: actualHasMore,
    total,
  };
}

/**
 * Get pagination parameters with defaults
 */
export function getPaginationParams(
  limit?: number,
  cursor?: string
): { limit: number; cursor?: string } {
  return {
    limit: Math.min(limit || 20, 100), // Max 100 items per page
    cursor,
  };
}

// ============================================================================
// SEARCH UTILITIES
// ============================================================================

/**
 * Normalize search query for consistent searching
 */
export function normalizeSearchQuery(query?: string): string | undefined {
  if (!query) return undefined;
  
  return query
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' '); // Replace multiple spaces with single space
}

/**
 * Check if text matches search query (supports both Bengali and English)
 */
export function matchesSearchQuery(
  text: LocalizedText,
  searchQuery: string
): boolean {
  const normalizedQuery = normalizeSearchQuery(searchQuery);
  if (!normalizedQuery) return true;

  const bnMatch = text.bn.toLowerCase().includes(normalizedQuery);
  const enMatch = text.en?.toLowerCase().includes(normalizedQuery) ?? false;
  
  return bnMatch || enMatch;
}

/**
 * Check if keywords match search query
 */
export function matchesKeywords(
  keywords: string[] | undefined,
  searchQuery: string
): boolean {
  if (!keywords || keywords.length === 0) return false;
  
  const normalizedQuery = normalizeSearchQuery(searchQuery);
  if (!normalizedQuery) return true;

  return keywords.some(keyword => 
    keyword.toLowerCase().includes(normalizedQuery)
  );
}

// ============================================================================
// SLUG UTILITIES
// ============================================================================

/**
 * Generate a URL-friendly slug from text
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Generate slug from localized text (prefers English, falls back to Bengali)
 */
export function generateSlugFromLocalizedText(text: LocalizedText): string {
  const sourceText = text.en || text.bn;
  return generateSlug(sourceText);
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate listing status transition
 */
export function isValidStatusTransition(
  currentStatus: ListingStatus,
  newStatus: ListingStatus
): boolean {
  const validTransitions: Record<ListingStatus, ListingStatus[]> = {
    draft: ["pending_review"],
    pending_review: ["published", "rejected", "draft"],
    published: ["rejected"], // Can only reject published listings
    rejected: ["pending_review"], // Can resubmit for review
  };

  return validTransitions[currentStatus]?.includes(newStatus) ?? false;
}

/**
 * Get sort field and direction for listings
 */
export function getListingSortConfig(sortBy: ListingSortBy): {
  field: string;
  direction: "asc" | "desc";
} {
  switch (sortBy) {
    case "newest":
      return { field: "_creationTime", direction: "desc" };
    case "featured":
      return { field: "isFeatured", direction: "desc" };
    case "name":
      return { field: "name.bn", direction: "asc" };
    case "relevance":
    default:
      return { field: "_creationTime", direction: "desc" };
  }
}

// ============================================================================
// TIME UTILITIES
// ============================================================================

/**
 * Get current timestamp
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * Create timestamps for new records
 */
export function createTimestamps(): { createdAt: number; updatedAt: number } {
  const now = getCurrentTimestamp();
  return { createdAt: now, updatedAt: now };
}

/**
 * Create update timestamp
 */
export function createUpdateTimestamp(): { updatedAt: number } {
  return { updatedAt: getCurrentTimestamp() };
}

// ============================================================================
// ID UTILITIES
// ============================================================================

/**
 * Generate a stable user ID
 */
export function generateStableUserId(): string {
  return `usr_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Generate a stable listing ID
 */
export function generateStableListingId(): string {
  return `lst_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// ============================================================================
// ARRAY UTILITIES
// ============================================================================

/**
 * Remove duplicates from array
 */
export function removeDuplicates<T>(array: T[]): T[] {
  return [...new Set(array)];
}

/**
 * Chunk array into smaller arrays
 */
export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// ============================================================================
// OBJECT UTILITIES
// ============================================================================

/**
 * Pick specific keys from object
 */
export function pick<T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

/**
 * Omit specific keys from object
 */
export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
}

// ============================================================================
// CONSTANTS
// ============================================================================

export const DEFAULT_PAGINATION_LIMIT = 20;
export const MAX_PAGINATION_LIMIT = 100;
export const DEFAULT_SEARCH_LIMIT = 20;
export const MAX_SEARCH_RESULTS = 1000;

@import "tailwindcss";

@theme {
  --color-light: #ffffff;
  --color-dark: #171717;
}

/* Custom animations for mobile HomeScreen */
@keyframes marquee {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-marquee {
  animation: marquee 15s linear infinite;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Line clamp utilities for news cards */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (prefers-color-scheme: dark) {
  body {
    color: var(--color-light);
    background: var(--color-dark);
    font-family: Arial, Helvetica, sans-serif;
  }
}

@media (prefers-color-scheme: light) {
  body {
    color: var(--color-dark);
    background: var(--color-light);
    font-family: Arial, Helvetica, sans-serif;
  }
}

// src/core/components/UserProfileCard.tsx
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";

interface UserProfileCardProps {
  userProfile: any;
}

export function UserProfileCard({ userProfile }: UserProfileCardProps) {
  const updateUser = useMutation(api.core.users.update);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    nameBn: userProfile.identity.name.bn || "",
    nameEn: userProfile.identity.name.en || "",
    phone: userProfile.contact.phone || "",
    language: userProfile.preferences.language || "bn",
    theme: userProfile.preferences.theme || "light",
  });

  const handleSave = async () => {
    try {
      await updateUser({
        updates: {
          name: {
            bn: editForm.nameBn,
            en: editForm.nameEn || undefined,
          },
          phone: editForm.phone || undefined,
          preferences: {
            language: editForm.language as "bn" | "en",
            theme: editForm.theme as "light" | "dark" | "auto",
          },
        },
      });
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to update profile:", error);
    }
  };

  const getRoleDisplay = (role: string) => {
    switch (role) {
      case "admin":
        return { bn: "প্রশাসক", en: "Administrator", color: "text-red-600 bg-red-100" };
      case "business_owner":
        return { bn: "ব্যবসায়ী", en: "Business Owner", color: "text-blue-600 bg-blue-100" };
      default:
        return { bn: "ব্যবহারকারী", en: "User", color: "text-green-600 bg-green-100" };
    }
  };

  const roleInfo = getRoleDisplay(userProfile.role);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold flex items-center gap-2">
          👤 <span>প্রোফাইল | Profile</span>
        </h2>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors"
        >
          {isEditing ? "❌ Cancel" : "✏️ Edit"}
        </button>
      </div>

      {!isEditing ? (
        <div className="space-y-4">
          {/* Profile Picture Placeholder */}
          <div className="flex justify-center mb-4">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
              {(userProfile.identity.name.bn || userProfile.identity.name.en || "U").charAt(0).toUpperCase()}
            </div>
          </div>

          {/* Name */}
          <div className="text-center">
            <h3 className="text-lg font-bold text-gray-800">
              {userProfile.identity.name.bn || "নাম নেই"}
            </h3>
            {userProfile.identity.name.en && (
              <p className="text-sm text-gray-600">{userProfile.identity.name.en}</p>
            )}
          </div>

          {/* Role Badge */}
          <div className="flex justify-center">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${roleInfo.color}`}>
              {roleInfo.bn} | {roleInfo.en}
            </span>
          </div>

          {/* Contact Info */}
          {userProfile.contact.phone && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>📞</span>
              <span>{userProfile.contact.phone}</span>
            </div>
          )}

          {/* Preferences */}
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">ভাষা | Language:</span>
              <span className="font-medium">
                {userProfile.preferences.language === "bn" ? "বাংলা | Bengali" : "English | ইংরেজি"}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">থিম | Theme:</span>
              <span className="font-medium capitalize">{userProfile.preferences.theme}</span>
            </div>
          </div>

          {/* Onboarding Status */}
          <div className={`p-3 rounded-lg ${
            userProfile.isOnboarded 
              ? "bg-green-50 border border-green-200" 
              : "bg-yellow-50 border border-yellow-200"
          }`}>
            <div className="flex items-center gap-2">
              <span>{userProfile.isOnboarded ? "✅" : "⚠️"}</span>
              <span className={`text-sm font-medium ${
                userProfile.isOnboarded ? "text-green-700" : "text-yellow-700"
              }`}>
                {userProfile.isOnboarded 
                  ? "প্রোফাইল সম্পূর্ণ | Profile Complete"
                  : "প্রোফাইল অসম্পূর্ণ | Profile Incomplete"
                }
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Edit Form */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              বাংলা নাম | Bengali Name *
            </label>
            <input
              type="text"
              value={editForm.nameBn}
              onChange={(e) => setEditForm({ ...editForm, nameBn: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="আপনার বাংলা নাম লিখুন"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ইংরেজি নাম | English Name
            </label>
            <input
              type="text"
              value={editForm.nameEn}
              onChange={(e) => setEditForm({ ...editForm, nameEn: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Your English name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ফোন নম্বর | Phone Number
            </label>
            <input
              type="tel"
              value={editForm.phone}
              onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="+8801XXXXXXXXX"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ভাষা | Language
            </label>
            <select
              value={editForm.language}
              onChange={(e) => setEditForm({ ...editForm, language: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="bn">বাংলা | Bengali</option>
              <option value="en">English | ইংরেজি</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              থিম | Theme
            </label>
            <select
              value={editForm.theme}
              onChange={(e) => setEditForm({ ...editForm, theme: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto</option>
            </select>
          </div>

          <div className="flex gap-2 pt-2">
            <button
              onClick={handleSave}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              💾 Save
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors font-medium"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

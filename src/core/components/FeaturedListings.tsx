// src/core/components/FeaturedListings.tsx
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { useState } from "react";

interface FeaturedListingsProps {
  selectedLocationId: Id<"locations"> | null;
}

export function FeaturedListings({ selectedLocationId }: FeaturedListingsProps) {
  const featuredListings = useQuery(api.directory.search.getFeaturedListings, { limit: 8 });
  const incrementViewCount = useMutation(api.directory.search.incrementListingViewCount);
  const [viewingListing, setViewingListing] = useState<any>(null);

  const handleViewListing = async (listing: any) => {
    try {
      await incrementViewCount({ listingId: listing._id });
      setViewingListing(listing);
    } catch (error) {
      console.error("Failed to increment view count:", error);
      setViewingListing(listing);
    }
  };

  if (featuredListings === undefined) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold mb-6">⭐ ফিচার্ড ব্যবসা | Featured Businesses</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Filter by location if selected
  const filteredListings = selectedLocationId 
    ? featuredListings.filter(listing => listing.locationId === selectedLocationId)
    : featuredListings;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold flex items-center gap-2">
          ⭐ <span>ফিচার্ড ব্যবসা | Featured Businesses</span>
        </h2>
        <div className="flex items-center gap-2">
          {selectedLocationId && (
            <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              নির্বাচিত এলাকার জন্য | For Selected Location
            </span>
          )}
          <span className="text-sm text-blue-600 bg-blue-100 px-3 py-1 rounded-full">
            {filteredListings.length} টি ব্যবসা
          </span>
        </div>
      </div>

      {filteredListings.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium mb-2">কোন ফিচার্ড ব্যবসা পাওয়া যায়নি</h3>
          <p className="text-sm">No featured businesses found</p>
          {selectedLocationId && (
            <p className="text-xs mt-2">নির্বাচিত এলাকায় | In the selected location</p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {filteredListings.map((listing) => (
            <div
              key={listing._id}
              className="bg-gradient-to-br from-white to-gray-50 rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer"
              onClick={() => handleViewListing(listing)}
            >
              {/* Featured Badge */}
              <div className="relative">
                <div className="absolute top-3 right-3 z-10">
                  <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-md">
                    ⭐ Featured
                  </span>
                </div>
                
                {/* Placeholder Image */}
                <div className="h-32 bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
                  <div className="text-white text-4xl">
                    {listing.name.bn.charAt(0)}
                  </div>
                </div>
              </div>

              <div className="p-4">
                {/* Business Name */}
                <h3 className="font-bold text-gray-800 mb-1 line-clamp-2">
                  {listing.name.bn}
                </h3>
                {listing.name.en && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-1">
                    {listing.name.en}
                  </p>
                )}

                {/* Contact Info */}
                <div className="space-y-2 mb-4">
                  {listing.contact?.phone && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span className="text-green-600">📞</span>
                      <span className="truncate">{listing.contact.phone}</span>
                    </div>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <div className="flex items-center gap-1">
                    <span>👁️</span>
                    <span>{listing.viewCount || 0} views</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>📅</span>
                    <span>
                      {new Date(listing._creationTime).toLocaleDateString('bn-BD')}
                    </span>
                  </div>
                </div>

                {/* Action Button */}
                <button className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                  বিস্তারিত দেখুন | View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* View All Button */}
      {filteredListings.length > 0 && (
        <div className="text-center mt-6">
          <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-md">
            সব ফিচার্ড ব্যবসা দেখুন | View All Featured Businesses
          </button>
        </div>
      )}

      {/* Listing Detail Modal */}
      {viewingListing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-gray-800">
                  {viewingListing.name.bn}
                </h2>
                <button
                  onClick={() => setViewingListing(null)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  ✕
                </button>
              </div>

              {/* Content */}
              <div className="space-y-4">
                {viewingListing.name.en && (
                  <p className="text-lg text-gray-600">{viewingListing.name.en}</p>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Contact Information */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-3">যোগাযোগ | Contact</h3>
                    <div className="space-y-2">
                      {viewingListing.contact?.phone && (
                        <div className="flex items-center gap-2">
                          <span>📞</span>
                          <span>{viewingListing.contact.phone}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Statistics */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold mb-3">পরিসংখ্যান | Statistics</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Views:</span>
                        <span>{viewingListing.viewCount || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <span className="capitalize">{viewingListing.status}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Featured:</span>
                        <span>{viewingListing.isFeatured ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Keywords */}
                {viewingListing.keywords && viewingListing.keywords.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">কীওয়ার্ড | Keywords</h3>
                    <div className="flex flex-wrap gap-2">
                      {viewingListing.keywords.map((keyword: string, index: number) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <button className="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                    📞 Call Now
                  </button>
                  <button className="flex-1 bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    📍 Get Directions
                  </button>
                  <button className="flex-1 bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium">
                    📤 Share
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

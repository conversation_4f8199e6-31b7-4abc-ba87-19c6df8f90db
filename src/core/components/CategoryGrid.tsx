// src/core/components/CategoryGrid.tsx
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { useState } from "react";

interface CategoryGridProps {
  selectedLocationId: Id<"locations"> | null;
}

export function CategoryGrid({ selectedLocationId }: CategoryGridProps) {
  const categories = useQuery(api.directory.categories.listCategories, { isActive: true });
  const [selectedCategory, setSelectedCategory] = useState<Id<"categories"> | null>(null);

  // Get listings for selected category and location
  const categoryListings = useQuery(
    api.directory.search.getListingsWithDetails,
    selectedCategory ? {
      categoryId: selectedCategory,
      locationId: selectedLocationId || undefined,
      status: "published",
      limit: 20
    } : "skip"
  );

  if (categories === undefined) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold mb-6">📂 ক্যাটেগরি | Categories</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-24 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const activeCategories = categories.filter(cat => cat.isActive);

  // Category icons mapping (you can expand this)
  const getCategoryIcon = (categoryName: string) => {
    const name = categoryName.toLowerCase();
    if (name.includes('food') || name.includes('খাবার') || name.includes('রেস্টুরেন্ট')) return '🍽️';
    if (name.includes('shop') || name.includes('দোকান') || name.includes('store')) return '🏪';
    if (name.includes('service') || name.includes('সেবা')) return '🔧';
    if (name.includes('health') || name.includes('স্বাস্থ্য') || name.includes('hospital')) return '🏥';
    if (name.includes('education') || name.includes('শিক্ষা') || name.includes('school')) return '🎓';
    if (name.includes('transport') || name.includes('যানবাহন')) return '🚗';
    if (name.includes('beauty') || name.includes('সৌন্দর্য')) return '💄';
    if (name.includes('electronics') || name.includes('ইলেকট্রনিক্স')) return '📱';
    return '📋';
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold flex items-center gap-2">
          📂 <span>ব্যবসার ধরন | Business Categories</span>
        </h2>
        <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
          {activeCategories.length} ক্যাটেগরি
        </span>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
        {activeCategories.map((category) => (
          <button
            key={category._id}
            onClick={() => setSelectedCategory(
              selectedCategory === category._id ? null : category._id
            )}
            className={`p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md ${
              selectedCategory === category._id
                ? 'border-blue-500 bg-blue-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
          >
            <div className="text-center">
              <div className="text-3xl mb-2">
                {getCategoryIcon(category.name.bn)}
              </div>
              <div className="text-sm font-medium text-gray-800 mb-1">
                {category.name.bn}
              </div>
              <div className="text-xs text-gray-500">
                {category.name.en}
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Selected Category Listings */}
      {selectedCategory && (
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">
              নির্বাচিত ক্যাটেগরির ব্যবসা | Selected Category Businesses
            </h3>
            <button
              onClick={() => setSelectedCategory(null)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              ✕ Close
            </button>
          </div>

          {categoryListings === undefined ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-32 bg-gray-200 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : categoryListings.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🔍</div>
              <p>এই ক্যাটেগরিতে কোন ব্যবসা পাওয়া যায়নি</p>
              <p className="text-sm">No businesses found in this category</p>
              {selectedLocationId && (
                <p className="text-xs mt-2">নির্বাচিত এলাকার জন্য | For selected location</p>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categoryListings.slice(0, 6).map((listing) => (
                <div
                  key={listing._id}
                  className="bg-gray-50 rounded-lg p-4 hover:shadow-md transition-shadow border border-gray-200"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-800 text-sm">
                      {listing.name.bn}
                    </h4>
                    {listing.isFeatured && (
                      <span className="text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">
                        ⭐ Featured
                      </span>
                    )}
                  </div>
                  
                  {listing.name.en && (
                    <p className="text-xs text-gray-600 mb-2">{listing.name.en}</p>
                  )}
                  
                  <div className="space-y-1 text-xs text-gray-600">
                    {listing.location && (
                      <div className="flex items-center gap-1">
                        <span>📍</span>
                        <span>{listing.location.name.bn}</span>
                      </div>
                    )}
                    
                    {listing.contact?.phone && (
                      <div className="flex items-center gap-1">
                        <span>📞</span>
                        <span>{listing.contact.phone}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-1">
                      <span>👁️</span>
                      <span>{listing.viewCount || 0} views</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {categoryListings && categoryListings.length > 6 && (
            <div className="text-center mt-4">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                আরো দেখুন | View More ({categoryListings.length - 6} more)
              </button>
            </div>
          )}
        </div>
      )}

      {/* Quick Category Stats */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-blue-600">{activeCategories.length}</div>
            <div className="text-xs text-gray-600">সক্রিয় ক্যাটেগরি | Active Categories</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600">
              {categories.filter(c => !c.isActive).length}
            </div>
            <div className="text-xs text-gray-600">নিষ্ক্রিয় | Inactive</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-600">
              {selectedCategory ? (categoryListings?.length || 0) : '—'}
            </div>
            <div className="text-xs text-gray-600">নির্বাচিত ক্যাটেগরিতে | In Selected</div>
          </div>
          <div>
            <div className="text-lg font-bold text-orange-600">
              {selectedLocationId ? 'স্থানীয়' : 'সব'}
            </div>
            <div className="text-xs text-gray-600">স্কোপ | Scope</div>
          </div>
        </div>
      </div>
    </div>
  );
}

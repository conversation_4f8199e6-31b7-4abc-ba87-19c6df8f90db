// src/core/components/HeroBanner.tsx
import { useState } from "react";

interface HeroBannerProps {
  userProfile: any;
}

export function HeroBanner({ userProfile }: HeroBannerProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const userName = userProfile.identity.name.en || userProfile.identity.name.bn || "User";
  const isOnboarded = userProfile.isOnboarded;

  return (
    <div className="relative bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-2xl shadow-xl overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-black bg-opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative px-8 py-12">
        <div className="max-w-4xl mx-auto text-center">
          {/* Main Heading */}
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            <span className="block text-yellow-300">হাইপারলোকাল বিডি</span>
            <span className="block text-2xl md:text-3xl font-normal mt-2 opacity-90">
              Hyperlocal Bangladesh
            </span>
          </h1>

          {/* Welcome Message */}
          <div className="mb-8">
            <p className="text-xl md:text-2xl mb-2">
              স্বাগতম, {userName}! 👋
            </p>
            <p className="text-lg opacity-90">
              আপনার এলাকার সবকিছু, এক ক্লিকে। Everything in your area, just a click away.
            </p>
            
            {!isOnboarded && (
              <div className="mt-4 p-3 bg-yellow-500 bg-opacity-20 rounded-lg border border-yellow-300 border-opacity-30">
                <p className="text-sm">
                  ⚠️ আপনার প্রোফাইল সম্পূর্ণ করুন | Please complete your profile setup
                </p>
              </div>
            )}
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <input
                type="text"
                placeholder="ব্যবসা, সেবা বা পণ্য খুঁজুন... | Search for businesses, services, or products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-4 text-lg text-gray-800 bg-white rounded-full shadow-lg focus:outline-none focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50 placeholder-gray-500"
              />
              <button className="absolute right-2 top-2 bottom-2 px-6 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors font-medium">
                🔍 খুঁজুন
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button className="px-8 py-3 bg-white text-blue-600 font-semibold rounded-full shadow-lg hover:bg-gray-100 transition-colors flex items-center gap-2">
              🏪 ব্যবসা খুঁজুন | Find Businesses
            </button>
            <button className="px-8 py-3 bg-yellow-500 text-white font-semibold rounded-full shadow-lg hover:bg-yellow-600 transition-colors flex items-center gap-2">
              📝 বিজ্ঞাপন দিন | Post Ad
            </button>
            <button className="px-8 py-3 bg-green-500 text-white font-semibold rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center gap-2">
              📱 সেবা নিন | Get Service
            </button>
          </div>

          {/* Quick Stats */}
          <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="bg-white bg-opacity-10 rounded-lg p-4">
              <div className="text-2xl font-bold">1000+</div>
              <div className="text-sm opacity-80">ব্যবসা | Businesses</div>
            </div>
            <div className="bg-white bg-opacity-10 rounded-lg p-4">
              <div className="text-2xl font-bold">50+</div>
              <div className="text-sm opacity-80">এলাকা | Areas</div>
            </div>
            <div className="bg-white bg-opacity-10 rounded-lg p-4">
              <div className="text-2xl font-bold">25+</div>
              <div className="text-sm opacity-80">ক্যাটেগরি | Categories</div>
            </div>
            <div className="bg-white bg-opacity-10 rounded-lg p-4">
              <div className="text-2xl font-bold">5000+</div>
              <div className="text-sm opacity-80">ব্যবহারকারী | Users</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

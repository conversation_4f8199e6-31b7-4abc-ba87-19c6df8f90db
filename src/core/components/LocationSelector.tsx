// src/core/components/LocationSelector.tsx
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { useState } from "react";

interface LocationSelectorProps {
  selectedLocationId: Id<"locations"> | null;
  onLocationChange: (locationId: Id<"locations"> | null) => void;
}

export function LocationSelector({ selectedLocationId, onLocationChange }: LocationSelectorProps) {
  // Get all locations by getting divisions, districts, and upazilas separately
  const divisions = useQuery(api.core.locations.listByType, { type: "division" });
  const districts = useQuery(api.core.locations.listByType, { type: "district" });
  const upazilas = useQuery(api.core.locations.listByType, { type: "upazila" });

  // Combine all locations
  const locations = divisions && districts && upazilas
    ? [...divisions, ...districts, ...upazilas]
    : undefined;
  const [selectedDivision, setSelectedDivision] = useState<Id<"locations"> | null>(null);
  const [selectedDistrict, setSelectedDistrict] = useState<Id<"locations"> | null>(null);

  if (locations === undefined) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold mb-4">📍 এলাকা নির্বাচন | Select Location</h2>
        <div className="animate-pulse space-y-3">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Group locations by type
  const divisionsFiltered = locations.filter(loc => loc.type === "division");
  const districtsFiltered = locations.filter(loc => loc.type === "district" && loc.parentId === selectedDivision);
  const upazilasFiltered = locations.filter(loc => loc.type === "upazila" && loc.parentId === selectedDistrict);

  const selectedLocation = selectedLocationId ? locations.find(loc => loc._id === selectedLocationId) : null;

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h2 className="text-xl font-bold mb-6 flex items-center gap-2">
        📍 <span>এলাকা নির্বাচন করুন | Select Your Location</span>
      </h2>

      {/* Current Selection Display */}
      {selectedLocation && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">নির্বাচিত এলাকা | Selected Location:</p>
              <p className="text-lg font-bold text-blue-800">
                {selectedLocation.name.bn} | {selectedLocation.name.en}
              </p>
              <p className="text-sm text-blue-600 capitalize">
                {selectedLocation.type === "division" ? "বিভাগ | Division" :
                 selectedLocation.type === "district" ? "জেলা | District" :
                 "উপজেলা | Upazila"}
              </p>
            </div>
            <button
              onClick={() => {
                onLocationChange(null);
                setSelectedDivision(null);
                setSelectedDistrict(null);
              }}
              className="px-3 py-1 text-sm bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors"
            >
              ✕ Clear
            </button>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* Division Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            বিভাগ নির্বাচন করুন | Select Division
          </label>
          <select
            value={selectedDivision || ""}
            onChange={(e) => {
              const divisionId = e.target.value as Id<"locations">;
              setSelectedDivision(divisionId || null);
              setSelectedDistrict(null);
              if (divisionId) {
                onLocationChange(divisionId);
              }
            }}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value="">-- বিভাগ নির্বাচন করুন | Choose Division --</option>
            {divisionsFiltered.map((division) => (
              <option key={division._id} value={division._id}>
                {division.name.bn} | {division.name.en}
              </option>
            ))}
          </select>
        </div>

        {/* District Selection */}
        {selectedDivision && districtsFiltered.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              জেলা নির্বাচন করুন | Select District
            </label>
            <select
              value={selectedDistrict || ""}
              onChange={(e) => {
                const districtId = e.target.value as Id<"locations">;
                setSelectedDistrict(districtId || null);
                if (districtId) {
                  onLocationChange(districtId);
                }
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">-- জেলা নির্বাচন করুন | Choose District --</option>
              {districtsFiltered.map((district) => (
                <option key={district._id} value={district._id}>
                  {district.name.bn} | {district.name.en}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Upazila Selection */}
        {selectedDistrict && upazilasFiltered.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              উপজেলা নির্বাচন করুন | Select Upazila
            </label>
            <select
              value={selectedLocationId || ""}
              onChange={(e) => {
                const upazilaId = e.target.value as Id<"locations">;
                onLocationChange(upazilaId || null);
              }}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
            >
              <option value="">-- উপজেলা নির্বাচন করুন | Choose Upazila --</option>
              {upazilasFiltered.map((upazila) => (
                <option key={upazila._id} value={upazila._id}>
                  {upazila.name.bn} | {upazila.name.en}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Location Stats */}
      <div className="mt-6 grid grid-cols-3 gap-4 text-center">
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-lg font-bold text-blue-600">{divisionsFiltered.length}</div>
          <div className="text-xs text-gray-600">বিভাগ | Divisions</div>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-lg font-bold text-green-600">
            {locations.filter(l => l.type === "district").length}
          </div>
          <div className="text-xs text-gray-600">জেলা | Districts</div>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <div className="text-lg font-bold text-purple-600">
            {locations.filter(l => l.type === "upazila").length}
          </div>
          <div className="text-xs text-gray-600">উপজেলা | Upazilas</div>
        </div>
      </div>
    </div>
  );
}

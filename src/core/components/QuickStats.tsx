// src/core/components/QuickStats.tsx
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";

interface QuickStatsProps {
  selectedLocationId: Id<"locations"> | null;
}

export function QuickStats({ selectedLocationId }: QuickStatsProps) {
  // Get dashboard stats (admin function, but we'll handle the error gracefully)
  const dashboardStats = useQuery(api.core.admin.getDashboardStats);

  // Get listings for the selected location
  const locationListings = useQuery(
    api.directory.search.getListingsWithDetails,
    selectedLocationId ? { locationId: selectedLocationId, status: "published", limit: 100 } : "skip"
  );

  // Get all categories
  const categories = useQuery(api.directory.categories.listCategories, { isActive: true });

  // Get all locations - combine all types
  const divisions = useQuery(api.core.locations.listByType, { type: "division" });
  const districts = useQuery(api.core.locations.listByType, { type: "district" });
  const upazilas = useQuery(api.core.locations.listByType, { type: "upazila" });

  const locations = divisions && districts && upazilas
    ? [...divisions, ...districts, ...upazilas]
    : undefined;

  const isLoading = dashboardStats === undefined || categories === undefined || locations === undefined;

  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-xl font-bold mb-6">📊 পরিসংখ্যান | Quick Stats</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-16 bg-gray-200 rounded-lg"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Calculate stats
  const totalListings = dashboardStats?.total || 0;
  const publishedListings = dashboardStats?.published || 0;
  const pendingListings = dashboardStats?.pending || 0;
  const locationListingsCount = locationListings?.length || 0;
  const totalCategories = categories?.length || 0;
  const totalLocations = locations?.length || 0;

  const stats = [
    {
      title: "মোট ব্যবসা",
      subtitle: "Total Businesses",
      value: selectedLocationId ? locationListingsCount : publishedListings,
      icon: "🏪",
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "ক্যাটেগরি",
      subtitle: "Categories",
      value: totalCategories,
      icon: "📂",
      color: "from-green-500 to-green-600",
      textColor: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "এলাকা",
      subtitle: "Locations",
      value: totalLocations,
      icon: "📍",
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "অপেক্ষমাণ",
      subtitle: "Pending Review",
      value: pendingListings,
      icon: "⏳",
      color: "from-yellow-500 to-yellow-600",
      textColor: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold flex items-center gap-2">
          📊 <span>পরিসংখ্যান | Quick Stats</span>
        </h2>
        {selectedLocationId && (
          <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
            নির্বাচিত এলাকার জন্য | For Selected Location
          </span>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-xl p-4 border border-opacity-20 hover:shadow-md transition-shadow`}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl">{stat.icon}</span>
              <div className={`text-2xl font-bold ${stat.textColor}`}>
                {stat.value.toLocaleString()}
              </div>
            </div>
            <div className="text-sm">
              <div className={`font-medium ${stat.textColor}`}>{stat.title}</div>
              <div className="text-gray-600 text-xs">{stat.subtitle}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Stats Row */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-gray-700">
            {((publishedListings / Math.max(totalListings, 1)) * 100).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-600">প্রকাশিত হার | Published Rate</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-gray-700">
            {Math.round(publishedListings / Math.max(totalCategories, 1))}
          </div>
          <div className="text-sm text-gray-600">প্রতি ক্যাটেগরিতে | Per Category</div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <div className="text-lg font-bold text-gray-700">
            {Math.round(publishedListings / Math.max(totalLocations, 1))}
          </div>
          <div className="text-sm text-gray-600">প্রতি এলাকায় | Per Location</div>
        </div>
      </div>

      {/* Status Indicator */}
      <div className="mt-4 flex items-center justify-center">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>সিস্টেম সক্রিয় | System Active</span>
          <span className="text-xs text-gray-400">
            • Last updated: {new Date().toLocaleTimeString('bn-BD')}
          </span>
        </div>
      </div>
    </div>
  );
}

// src/core/HomeScreen.tsx - Mobile-First FAANG Style Design
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { AdminPanel } from "./AdminPanel";
import { UserManagementPanel } from "./UserManagementPanel";
import { DirectorySearchPage } from "../features/directory";
import { AdminRoute } from "../components/ProtectedRoute";
import { EmergencyAlertDisplay } from "../components/emergency/EmergencyAlertDisplay";
import { UserProfileEdit } from "../components/profile/UserProfileEdit";
import { useState } from "react";

export default function HomeScreen() {
  const userProfile = useQuery(api.core.users.getCurrent);
  const [activePanel, setActivePanel] = useState<
    "home" | "admin" | "users" | "directory" | "profile"
  >("home");
  // Fetch data from Convex
  const alerts = useQuery(api.core.homescreen.getAlerts);
  const serviceCategories = useQuery(api.core.homescreen.getServiceCategories);
  const localNews = useQuery(api.core.homescreen.getLocalNews);
  const promoBanners = useQuery(api.core.homescreen.getPromoBanners);
  const testimonials = useQuery(api.core.homescreen.getCommunityTestimonials);

  // Handle loading and error states properly
  if (userProfile === undefined) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center px-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your profile...</p>
        </div>
      </div>
    );
  }

  if (userProfile === null) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center px-4">
          <div className="text-4xl mb-4">😔</div>
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Profile Not Found</h2>
          <p className="text-gray-600 text-sm">Please sign in again to continue</p>
        </div>
      </div>
    );
  }

  const isAdmin = userProfile.role === "admin";

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Location Dropdown */}
            <div className="flex items-center space-x-2">
              <div className="text-blue-600">📍</div>
              <div>
                <div className="text-xs text-gray-500">Location</div>
                <div className="text-sm font-medium text-gray-800 flex items-center">
                  Alfadanga, Faridpur
                  <svg className="w-4 h-4 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Profile */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-sm font-medium">
                  {(userProfile as any).identity?.name?.en?.charAt(0) || (userProfile as any).identity?.name?.bn?.charAt(0) || "U"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pb-20">
        {activePanel === "home" && (
          <div className="space-y-4">
            {/* Emergency Alerts - Critical Safety Information */}
            <EmergencyAlertDisplay />

            {/* Today's Alerts - Critical Information */}
            <TodayAlerts alerts={alerts} />

            {/* Promotional Banner */}
            <PromoBanner banners={promoBanners} />

            {/* Essential Services */}
            <ServiceCategories categories={serviceCategories} />

            {/* Local News */}
            <LocalNews news={localNews} />

            {/* Community Testimonial */}
            <CommunityHero testimonials={testimonials} />
          </div>
        )}

        {activePanel === "directory" && <DirectorySearchPage />}
        {activePanel === "admin" && (
          <AdminRoute>
            <AdminPanel />
          </AdminRoute>
        )}
        {activePanel === "users" && (
          <AdminRoute>
            <UserManagementPanel />
          </AdminRoute>
        )}
        {activePanel === "profile" && <UserProfileEdit />}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="flex">
          <button
            onClick={() => setActivePanel("home")}
            className={`flex-1 py-3 px-2 text-center ${
              activePanel === "home"
                ? "text-blue-600 bg-blue-50"
                : "text-gray-600"
            }`}
          >
            <div className="text-lg mb-1">🏠</div>
            <div className="text-xs font-medium">Home</div>
          </button>

          <button
            onClick={() => setActivePanel("directory")}
            className={`flex-1 py-3 px-2 text-center ${
              activePanel === "directory"
                ? "text-blue-600 bg-blue-50"
                : "text-gray-600"
            }`}
          >
            <div className="text-lg mb-1">🔍</div>
            <div className="text-xs font-medium">Search</div>
          </button>

          <button
            onClick={() => setActivePanel("profile")}
            className={`flex-1 py-3 px-2 text-center ${
              activePanel === "profile"
                ? "text-blue-600 bg-blue-50"
                : "text-gray-600"
            }`}
          >
            <div className="text-lg mb-1">👤</div>
            <div className="text-xs font-medium">Profile</div>
          </button>

          {isAdmin && (
            <>
              <button
                onClick={() => setActivePanel("admin")}
                className={`flex-1 py-3 px-2 text-center ${
                  activePanel === "admin"
                    ? "text-blue-600 bg-blue-50"
                    : "text-gray-600"
                }`}
              >
                <div className="text-lg mb-1">⚙️</div>
                <div className="text-xs font-medium">Admin</div>
              </button>
              <button
                onClick={() => setActivePanel("users")}
                className={`flex-1 py-3 px-2 text-center ${
                  activePanel === "users"
                    ? "text-blue-600 bg-blue-50"
                    : "text-gray-600"
                }`}
              >
                <div className="text-lg mb-1">👥</div>
                <div className="text-xs font-medium">Users</div>
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Today's Alerts Component with Moving Headlines
function TodayAlerts({ alerts }: { alerts: any[] | undefined }) {
  if (!alerts || alerts.length === 0) {
    return null;
  }

  // Separate critical alerts for moving ticker
  const criticalAlerts = alerts.filter(alert => alert.type === "warning");
  const regularAlerts = alerts.filter(alert => alert.type !== "warning");

  return (
    <div className="pt-4">
      {/* Moving Critical Alerts Ticker */}
      {criticalAlerts.length > 0 && (
        <div className="bg-red-600 text-white py-2 overflow-hidden relative">
          <div className="flex items-center">
            <div className="px-4 flex items-center space-x-2 flex-shrink-0">
              <span className="text-sm font-bold">🚨 URGENT</span>
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="animate-marquee whitespace-nowrap">
                {criticalAlerts.map((alert, index) => (
                  <span key={alert._id} className="text-sm font-medium">
                    {alert.message}
                    {index < criticalAlerts.length - 1 && " • "}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Regular Alerts */}
      {regularAlerts.length > 0 && (
        <div className="px-4 pt-4">
          <div className="space-y-2">
            {regularAlerts.map((alert) => (
              <div
                key={alert._id}
                className="bg-blue-50 border border-blue-200 p-3 rounded-lg flex items-start space-x-3"
              >
                <div className="text-lg">{alert.icon}</div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-800">
                    {alert.message}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Enhanced Promotional Banner Component
function PromoBanner({ banners }: { banners: any[] | undefined }) {
  if (!banners || banners.length === 0) {
    return null;
  }

  const banner = banners[0]; // Show first active banner

  return (
    <div className="px-4">
      <div
        className="rounded-2xl p-5 text-white relative overflow-hidden shadow-lg"
        style={{ background: `linear-gradient(135deg, ${banner.bgColor || '#6366f1'}, ${banner.bgColor || '#8b5cf6'})` }}
      >
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white transform translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white transform -translate-x-12 translate-y-12"></div>
        </div>

        {/* Content */}
        <div className="relative flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-bold mb-1">{banner.title}</h3>
            <p className="text-sm opacity-90 mb-3">{banner.subtitle}</p>
            <button className="bg-white bg-opacity-20 backdrop-blur-sm text-white text-xs font-semibold px-4 py-2 rounded-full border border-white border-opacity-30 hover:bg-opacity-30 transition-all">
              Claim Now →
            </button>
          </div>
          <div className="text-4xl ml-4">{banner.icon}</div>
        </div>
      </div>
    </div>
  );
}

// Service Categories Component with Enhanced Design
function ServiceCategories({ categories }: { categories: any[] | undefined }) {
  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="px-4">
      <h2 className="text-lg font-bold text-gray-800 mb-4">Essential Services</h2>
      <div className="grid grid-cols-4 gap-3">
        {categories.map((category) => (
          <button
            key={category._id}
            className="group bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex flex-col items-center space-y-2 hover:shadow-lg hover:scale-105 transition-all duration-200 active:scale-95"
            style={{ backgroundColor: `${category.color}10` }}
          >
            {/* Icon with background circle */}
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center text-white text-xl font-bold shadow-sm"
              style={{ backgroundColor: category.color }}
            >
              {category.icon}
            </div>

            {/* Service name */}
            <div className="text-xs font-semibold text-gray-800 text-center leading-tight">
              {category.name}
            </div>

            {/* Bengali name */}
            {category.bnName && (
              <div className="text-xs text-gray-500 text-center leading-tight">
                {category.bnName}
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}

// Local News Component
function LocalNews({ news }: { news: any[] | undefined }) {
  if (!news || news.length === 0) {
    return null;
  }

  return (
    <div className="px-4">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-bold text-gray-800">Local News</h2>
        <button className="text-blue-600 text-sm font-medium">See All</button>
      </div>
      <div className="space-y-3">
        {news.slice(0, 2).map((newsItem) => (
          <div key={newsItem._id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="flex">
              {/* News Image */}
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center flex-shrink-0">
                {newsItem.imageUrl ? (
                  <img src={newsItem.imageUrl} alt={newsItem.title} className="w-full h-full object-cover" />
                ) : (
                  <span className="text-2xl">📰</span>
                )}
              </div>

              {/* News Content */}
              <div className="flex-1 p-3">
                <h3 className="font-semibold text-gray-800 text-sm mb-1 leading-tight line-clamp-2">
                  {newsItem.title}
                </h3>
                <p className="text-xs text-gray-600 leading-relaxed line-clamp-2">
                  {newsItem.excerpt}
                </p>
                <div className="flex items-center justify-between mt-2">
                  <span className="text-xs text-gray-400">
                    {new Date(newsItem.publishedAt || newsItem._creationTime).toLocaleDateString('bn-BD')}
                  </span>
                  <span className="text-xs text-blue-600 font-medium">পড়ুন</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Community Hero Component
function CommunityHero({ testimonials }: { testimonials: any[] | undefined }) {
  if (!testimonials || testimonials.length === 0) {
    return null;
  }

  const testimonial = testimonials[0]; // Show first testimonial

  return (
    <div className="px-4 pb-4">
      <div className="bg-gradient-to-r from-green-400 to-blue-500 rounded-xl p-4 text-white">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <span className="text-lg">😊</span>
          </div>
          <div className="flex-1">
            <p className="font-medium text-sm mb-1">
              "{testimonial.quote}"
            </p>
            <p className="text-xs opacity-90">
              {testimonial.author}, {testimonial.location}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

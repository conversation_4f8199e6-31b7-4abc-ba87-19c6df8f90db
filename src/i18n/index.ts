// Internationalization system for Bengali and English
import { createContext, useContext } from 'react';

export type Language = 'bn' | 'en';

export interface Translations {
  // Common
  loading: string;
  error: string;
  retry: string;
  cancel: string;
  save: string;
  delete: string;
  edit: string;
  
  // Navigation
  home: string;
  search: string;
  profile: string;
  settings: string;
  
  // Home Screen
  todayAlerts: string;
  localNews: string;
  services: string;
  community: string;
  
  // Services
  prepaidRecharge: string;
  billPayment: string;
  police: string;
  hospital: string;
  education: string;
  transport: string;
  
  // Alerts
  urgent: string;
  powerCut: string;
  waterLog: string;
  event: string;
  
  // Auth
  signIn: string;
  signOut: string;
  phoneNumber: string;
  enterPhone: string;
  
  // Errors
  networkError: string;
  serverError: string;
  notFound: string;
  accessDenied: string;
}

const translations: Record<Language, Translations> = {
  bn: {
    // Common
    loading: 'লোড হচ্ছে...',
    error: 'ত্রুটি',
    retry: 'আবার চেষ্টা করুন',
    cancel: 'বাতিল',
    save: 'সংরক্ষণ',
    delete: 'মুছুন',
    edit: 'সম্পাদনা',
    
    // Navigation
    home: 'হোম',
    search: 'খুঁজুন',
    profile: 'প্রোফাইল',
    settings: 'সেটিংস',
    
    // Home Screen
    todayAlerts: 'আজকের সতর্কতা',
    localNews: 'স্থানীয় সংবাদ',
    services: 'সেবাসমূহ',
    community: 'কমিউনিটি',
    
    // Services
    prepaidRecharge: 'প্রিপেইড রিচার্জ',
    billPayment: 'বিল পেমেন্ট',
    police: 'পুলিশ',
    hospital: 'হাসপাতাল',
    education: 'শিক্ষা',
    transport: 'পরিবহন',
    
    // Alerts
    urgent: 'জরুরি',
    powerCut: 'বিদ্যুৎ বিভ্রাট',
    waterLog: 'পানি জমা',
    event: 'অনুষ্ঠান',
    
    // Auth
    signIn: 'সাইন ইন',
    signOut: 'সাইন আউট',
    phoneNumber: 'ফোন নম্বর',
    enterPhone: 'আপনার ফোন নম্বর দিন',
    
    // Errors
    networkError: 'নেটওয়ার্ক সমস্যা',
    serverError: 'সার্ভার সমস্যা',
    notFound: 'পাওয়া যায়নি',
    accessDenied: 'প্রবেশাধিকার নেই',
  },
  
  en: {
    // Common
    loading: 'Loading...',
    error: 'Error',
    retry: 'Retry',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    
    // Navigation
    home: 'Home',
    search: 'Search',
    profile: 'Profile',
    settings: 'Settings',
    
    // Home Screen
    todayAlerts: "Today's Alerts",
    localNews: 'Local News',
    services: 'Services',
    community: 'Community',
    
    // Services
    prepaidRecharge: 'Prepaid Recharge',
    billPayment: 'Bill Payment',
    police: 'Police',
    hospital: 'Hospital',
    education: 'Education',
    transport: 'Transport',
    
    // Alerts
    urgent: 'URGENT',
    powerCut: 'Power Cut',
    waterLog: 'Water Logging',
    event: 'Event',
    
    // Auth
    signIn: 'Sign In',
    signOut: 'Sign Out',
    phoneNumber: 'Phone Number',
    enterPhone: 'Enter your phone number',
    
    // Errors
    networkError: 'Network Error',
    serverError: 'Server Error',
    notFound: 'Not Found',
    accessDenied: 'Access Denied',
  }
};

interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: keyof Translations) => string;
  isRTL: boolean;
}

export const I18nContext = createContext<I18nContextType | undefined>(undefined);

export const useI18n = () => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18n must be used within I18nProvider');
  }
  return context;
};

export const getTranslation = (language: Language, key: keyof Translations): string => {
  return translations[language][key] || translations.en[key] || key;
};

export const isRTLLanguage = (language: Language): boolean => {
  // Bengali is written left-to-right, but we might add Arabic/Urdu later
  return false;
};

export { translations };

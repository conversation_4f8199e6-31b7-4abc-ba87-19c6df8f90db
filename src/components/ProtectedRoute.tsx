// src/components/ProtectedRoute.tsx
import { ReactNode } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

interface ProtectedRouteProps {
  children: ReactNode;
  requireAdmin?: boolean;
  fallback?: ReactNode;
}

export function ProtectedRoute({ 
  children, 
  requireAdmin = false, 
  fallback 
}: ProtectedRouteProps) {
  const userProfile = useQuery(api.core.users.getCurrent);

  // Loading state
  if (userProfile === undefined) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-gray-50">
          <div className="text-center px-4">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying access...</p>
          </div>
        </div>
      )
    );
  }

  // User not found
  if (userProfile === null) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center px-4">
          <div className="text-4xl mb-4">🔒</div>
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Access Denied</h2>
          <p className="text-gray-600 text-sm">Please sign in to continue</p>
        </div>
      </div>
    );
  }

  // Admin access required but user is not admin
  if (requireAdmin && userProfile.role !== "admin") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center px-4">
          <div className="text-4xl mb-4">👮‍♂️</div>
          <h2 className="text-lg font-semibold text-gray-800 mb-2">Admin Access Required</h2>
          <p className="text-gray-600 text-sm">You don't have permission to access this area</p>
        </div>
      </div>
    );
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Convenience wrapper for admin-only routes
export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <ProtectedRoute requireAdmin={true}>
      {children}
    </ProtectedRoute>
  );
}

// src/components/SafeInput.tsx
// Safe input components with built-in sanitization and validation

import { useState, useEffect } from 'react';
import { sanitizeInput, validation } from '../utils/sanitization';

interface SafeInputProps {
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'phone' | 'name' | 'search' | 'url';
  placeholder?: string;
  className?: string;
  maxLength?: number;
  required?: boolean;
  disabled?: boolean;
  label?: string;
  error?: string;
}

export function SafeInput({
  value,
  onChange,
  type = 'text',
  placeholder,
  className = '',
  maxLength = 1000,
  required = false,
  disabled = false,
  label,
  error
}: SafeInputProps) {
  const [localValue, setLocalValue] = useState(value);
  const [validationError, setValidationError] = useState<string>('');

  // Sync with external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    
    // Sanitize input
    const sanitized = sanitizeInput(rawValue, type);
    
    // Validate input
    let errorMessage = '';
    
    if (required && !sanitized.trim()) {
      errorMessage = 'This field is required';
    } else if (validation.isDangerous(rawValue)) {
      errorMessage = 'Input contains potentially dangerous content';
    } else if (validation.isTooLong(sanitized, maxLength)) {
      errorMessage = `Input is too long (max ${maxLength} characters)`;
    } else if (type === 'name' && sanitized && !validation.isValidName(sanitized)) {
      errorMessage = 'Please enter a valid name';
    } else if (type === 'phone' && sanitized && !validation.isValidPhone(sanitized)) {
      errorMessage = 'Please enter a valid phone number';
    }

    setValidationError(errorMessage);
    setLocalValue(sanitized);
    onChange(sanitized);
  };

  const inputClassName = `
    w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500
    ${error || validationError ? 'border-red-500' : 'border-gray-300'}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    ${className}
  `.trim();

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        type="text"
        value={localValue}
        onChange={handleChange}
        placeholder={placeholder}
        className={inputClassName}
        maxLength={maxLength}
        required={required}
        disabled={disabled}
      />
      
      {(error || validationError) && (
        <p className="text-sm text-red-600">
          {error || validationError}
        </p>
      )}
      
      {maxLength && localValue.length > maxLength * 0.8 && (
        <p className="text-xs text-gray-500">
          {localValue.length}/{maxLength} characters
        </p>
      )}
    </div>
  );
}

interface SafeTextAreaProps extends Omit<SafeInputProps, 'type'> {
  rows?: number;
}

export function SafeTextArea({
  value,
  onChange,
  placeholder,
  className = '',
  maxLength = 2000,
  required = false,
  disabled = false,
  label,
  error,
  rows = 4
}: SafeTextAreaProps) {
  const [localValue, setLocalValue] = useState(value);
  const [validationError, setValidationError] = useState<string>('');

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const rawValue = e.target.value;
    
    // Sanitize input (using text type for textarea)
    const sanitized = sanitizeInput(rawValue, 'text');
    
    // Validate input
    let errorMessage = '';
    
    if (required && !sanitized.trim()) {
      errorMessage = 'This field is required';
    } else if (validation.isDangerous(rawValue)) {
      errorMessage = 'Input contains potentially dangerous content';
    } else if (validation.isTooLong(sanitized, maxLength)) {
      errorMessage = `Input is too long (max ${maxLength} characters)`;
    }

    setValidationError(errorMessage);
    setLocalValue(sanitized);
    onChange(sanitized);
  };

  const textareaClassName = `
    w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-vertical
    ${error || validationError ? 'border-red-500' : 'border-gray-300'}
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    ${className}
  `.trim();

  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        value={localValue}
        onChange={handleChange}
        placeholder={placeholder}
        className={textareaClassName}
        maxLength={maxLength}
        required={required}
        disabled={disabled}
        rows={rows}
      />
      
      {(error || validationError) && (
        <p className="text-sm text-red-600">
          {error || validationError}
        </p>
      )}
      
      <div className="flex justify-between text-xs text-gray-500">
        <span>
          {localValue.length}/{maxLength} characters
        </span>
        {maxLength && localValue.length > maxLength * 0.9 && (
          <span className="text-orange-500">
            Approaching limit
          </span>
        )}
      </div>
    </div>
  );
}

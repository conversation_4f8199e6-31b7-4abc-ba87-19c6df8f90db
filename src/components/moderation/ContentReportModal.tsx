// Content Report Modal - Allow users to report inappropriate content
import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { LoadingSpinner } from "../LoadingSpinner";
import { useToast } from "../Toast";

interface ContentReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentType: "post" | "comment" | "listing";
  contentId: string;
  contentPreview?: string;
}

export function ContentReportModal({ 
  isOpen, 
  onClose, 
  contentType, 
  contentId, 
  contentPreview 
}: ContentReportModalProps) {
  const { showToast } = useToast();
  const [reason, setReason] = useState<"spam" | "inappropriate" | "harassment" | "misinformation" | "other">("spam");
  const [description, setDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [language, setLanguage] = useState<"bn" | "en">("bn");

  const reportContent = useMutation(api.core.moderation.reportContent);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!description.trim()) {
      showToast(
        language === "bn" ? "অনুগ্রহ করে বিস্তারিত বর্ণনা দিন" : "Please provide a detailed description",
        "error"
      );
      return;
    }

    setIsSubmitting(true);
    try {
      await reportContent({
        contentType,
        contentId,
        reason,
        description: description.trim(),
      });
      
      showToast(
        language === "bn" 
          ? "আপনার রিপোর্ট সফলভাবে জমা দেওয়া হয়েছে। আমরা শীঘ্রই এটি পর্যালোচনা করব।"
          : "Your report has been submitted successfully. We will review it shortly.",
        "success"
      );
      
      // Reset form and close modal
      setReason("spam");
      setDescription("");
      onClose();
    } catch (error) {
      showToast(
        language === "bn" 
          ? "রিপোর্ট জমা দিতে ব্যর্থ। অনুগ্রহ করে আবার চেষ্টা করুন।"
          : "Failed to submit report. Please try again.",
        "error"
      );
      console.error("Report submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const reasonOptions = {
    spam: {
      bn: "স্প্যাম বা অবাঞ্ছিত বিজ্ঞাপন",
      en: "Spam or unwanted advertising"
    },
    inappropriate: {
      bn: "অনুপযুক্ত বা আপত্তিকর বিষয়বস্তু",
      en: "Inappropriate or offensive content"
    },
    harassment: {
      bn: "হয়রানি বা বুলিং",
      en: "Harassment or bullying"
    },
    misinformation: {
      bn: "ভুল তথ্য বা গুজব",
      en: "Misinformation or false information"
    },
    other: {
      bn: "অন্যান্য",
      en: "Other"
    }
  };

  const contentTypeLabels = {
    post: { bn: "পোস্ট", en: "Post" },
    comment: { bn: "মন্তব্য", en: "Comment" },
    listing: { bn: "তালিকা", en: "Listing" }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">🚩</span>
            <div>
              <h2 className="text-lg font-bold text-gray-900">
                {language === "bn" ? "বিষয়বস্তু রিপোর্ট করুন" : "Report Content"}
              </h2>
              <p className="text-sm text-gray-600">
                {language === "bn" 
                  ? `${contentTypeLabels[contentType].bn} রিপোর্ট করুন`
                  : `Report this ${contentTypeLabels[contentType].en.toLowerCase()}`
                }
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Language Toggle */}
            <button
              onClick={() => setLanguage(language === "bn" ? "en" : "bn")}
              className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200 transition-colors"
            >
              {language === "bn" ? "EN" : "বাং"}
            </button>
            
            {/* Close Button */}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <span className="text-xl">×</span>
            </button>
          </div>
        </div>

        {/* Content Preview */}
        {contentPreview && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <p className="text-sm text-gray-600 mb-2">
              {language === "bn" ? "রিপোর্ট করা বিষয়বস্তু:" : "Content being reported:"}
            </p>
            <div className="bg-white p-3 rounded border text-sm text-gray-800 max-h-20 overflow-y-auto">
              {contentPreview}
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              {language === "bn" ? "রিপোর্টের কারণ নির্বাচন করুন:" : "Select reason for reporting:"}
            </label>
            <div className="space-y-3">
              {Object.entries(reasonOptions).map(([key, labels]) => (
                <label key={key} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="reason"
                    value={key}
                    checked={reason === key}
                    onChange={(e) => setReason(e.target.value as any)}
                    className="mt-1 text-red-600 focus:ring-red-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {labels[language]}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {language === "bn" 
                ? "বিস্তারিত বর্ণনা (প্রয়োজনীয়):"
                : "Detailed description (required):"
              }
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
              placeholder={
                language === "bn"
                  ? "অনুগ্রহ করে সমস্যাটি বিস্তারিতভাবে বর্ণনা করুন..."
                  : "Please describe the issue in detail..."
              }
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              {language === "bn"
                ? "আপনার রিপোর্ট আমাদের মডারেশন টিম পর্যালোচনা করবে।"
                : "Your report will be reviewed by our moderation team."
              }
            </p>
          </div>

          {/* Community Guidelines Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <span className="text-blue-600 text-lg">ℹ️</span>
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">
                  {language === "bn" ? "কমিউনিটি গাইডলাইন" : "Community Guidelines"}
                </p>
                <p>
                  {language === "bn"
                    ? "আমরা একটি নিরাপদ এবং সম্মানজনক কমিউনিটি বজায় রাখতে প্রতিশ্রুতিবদ্ধ। মিথ্যা রিপোর্ট জমা দেওয়া থেকে বিরত থাকুন।"
                    : "We are committed to maintaining a safe and respectful community. Please refrain from submitting false reports."
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
            >
              {language === "bn" ? "বাতিল" : "Cancel"}
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !description.trim()}
              className="px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>{language === "bn" ? "জমা দেওয়া হচ্ছে..." : "Submitting..."}</span>
                </>
              ) : (
                <>
                  <span>🚩</span>
                  <span>{language === "bn" ? "রিপোর্ট জমা দিন" : "Submit Report"}</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

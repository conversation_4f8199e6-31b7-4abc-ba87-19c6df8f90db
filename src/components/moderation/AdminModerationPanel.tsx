// Admin Moderation Panel - Handle content moderation reports
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { LoadingSpinner } from "../LoadingSpinner";
import { useToast } from "../Toast";

interface ModerationReport {
  _id: string;
  reporterId: string;
  contentType: "post" | "comment" | "listing";
  contentId: string;
  reason: "spam" | "inappropriate" | "harassment" | "misinformation" | "other";
  description: string;
  status: "pending" | "resolved" | "actioned";
  createdAt: number;
  moderatorId?: string;
  moderatorAction?: "approve" | "remove" | "warn";
  adminNotes?: string;
}

export function AdminModerationPanel() {
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState<"pending" | "guidelines" | "stats">("pending");
  const [selectedReport, setSelectedReport] = useState<ModerationReport | null>(null);
  const [adminNotes, setAdminNotes] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Convex queries and mutations
  const pendingReports = useQuery(api.core.moderation.getPendingReports, { limit: 20 });
  const communityGuidelines = useQuery(api.core.moderation.getCommunityGuidelines, {});
  const moderateContent = useMutation(api.core.moderation.moderateContent);

  const handleModerationAction = async (
    reportId: string, 
    action: "approve" | "remove" | "warn"
  ) => {
    if (!adminNotes.trim() && action !== "approve") {
      showToast("Please provide admin notes for this action", "error");
      return;
    }

    setIsProcessing(true);
    try {
      await moderateContent({
        reportId,
        action,
        adminNotes: adminNotes.trim() || undefined,
      });
      
      showToast(`Content ${action}d successfully`, "success");
      setSelectedReport(null);
      setAdminNotes("");
    } catch (error) {
      showToast("Failed to process moderation action", "error");
      console.error("Moderation action error:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getReasonLabel = (reason: string) => {
    const labels = {
      spam: "Spam/Advertising",
      inappropriate: "Inappropriate Content",
      harassment: "Harassment/Bullying",
      misinformation: "Misinformation",
      other: "Other"
    };
    return labels[reason as keyof typeof labels] || reason;
  };

  const getReasonColor = (reason: string) => {
    const colors = {
      spam: "bg-yellow-100 text-yellow-800 border-yellow-200",
      inappropriate: "bg-red-100 text-red-800 border-red-200",
      harassment: "bg-purple-100 text-purple-800 border-purple-200",
      misinformation: "bg-orange-100 text-orange-800 border-orange-200",
      other: "bg-gray-100 text-gray-800 border-gray-200"
    };
    return colors[reason as keyof typeof colors] || colors.other;
  };

  const getContentTypeIcon = (contentType: string) => {
    const icons = {
      post: "📝",
      comment: "💬",
      listing: "🏪"
    };
    return icons[contentType as keyof typeof icons] || "📄";
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: "pending", label: "Pending Reports", icon: "🚩", count: pendingReports?.page?.length || 0 },
            { id: "guidelines", label: "Community Guidelines", icon: "📋" },
            { id: "stats", label: "Moderation Stats", icon: "📊" },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? "border-red-500 text-red-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
              {tab.count !== undefined && tab.count > 0 && (
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {/* Pending Reports Tab */}
        {activeTab === "pending" && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Pending Moderation Reports</h2>
            
            {pendingReports === undefined ? (
              <LoadingSpinner text="Loading reports..." />
            ) : !pendingReports?.page || pendingReports.page.length === 0 ? (
              <div className="text-center py-8">
                <span className="text-6xl mb-4 block">🎉</span>
                <p className="text-gray-500 text-lg">No pending reports!</p>
                <p className="text-gray-400 text-sm">All content is properly moderated.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {pendingReports.page.map((report: ModerationReport) => (
                  <div key={report._id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-start space-x-3">
                        <span className="text-2xl">{getContentTypeIcon(report.contentType)}</span>
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900 capitalize">
                              {report.contentType} Report
                            </h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getReasonColor(report.reason)}`}>
                              {getReasonLabel(report.reason)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{report.description}</p>
                          <div className="text-xs text-gray-500">
                            <span>Content ID: {report.contentId}</span>
                            <span className="ml-4">
                              Reported: {new Date(report.createdAt).toLocaleString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => setSelectedReport(report)}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                      >
                        Review
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Community Guidelines Tab */}
        {activeTab === "guidelines" && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Community Guidelines</h2>
            
            {communityGuidelines === undefined ? (
              <LoadingSpinner text="Loading guidelines..." />
            ) : (
              <div className="space-y-6">
                {communityGuidelines?.map((guideline: any, index: number) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      {guideline.title}
                    </h3>
                    <p className="text-gray-700 mb-4">{guideline.description}</p>
                    
                    {guideline.examples && guideline.examples.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-800 mb-2">Examples:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {guideline.examples.map((example: string, exIndex: number) => (
                            <li key={exIndex} className="text-sm text-gray-600">{example}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Statistics Tab */}
        {activeTab === "stats" && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Moderation Statistics</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-yellow-800">Pending Reports</h3>
                <p className="text-2xl font-bold text-yellow-900">
                  {pendingReports?.page?.length || 0}
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-green-800">Resolved Today</h3>
                <p className="text-2xl font-bold text-green-900">0</p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800">Total Reports</h3>
                <p className="text-2xl font-bold text-blue-900">
                  {pendingReports?.page?.length || 0}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Report Review Modal */}
      {selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-bold text-gray-900">Review Report</h3>
                <button
                  onClick={() => setSelectedReport(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="text-xl">×</span>
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Report Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Content Type:</span>
                    <span className="ml-2 capitalize">{selectedReport.contentType}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Reason:</span>
                    <span className="ml-2">{getReasonLabel(selectedReport.reason)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Content ID:</span>
                    <span className="ml-2 font-mono text-xs">{selectedReport.contentId}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Reported:</span>
                    <span className="ml-2">{new Date(selectedReport.createdAt).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Report Description */}
              <div>
                <h4 className="font-medium text-gray-700 mb-2">Report Description:</h4>
                <div className="bg-white border border-gray-200 rounded-lg p-3 text-sm text-gray-800">
                  {selectedReport.description}
                </div>
              </div>

              {/* Admin Notes */}
              <div>
                <label className="block font-medium text-gray-700 mb-2">
                  Admin Notes:
                </label>
                <textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add notes about your decision..."
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => handleModerationAction(selectedReport._id, "approve")}
                  disabled={isProcessing}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  {isProcessing ? <LoadingSpinner size="sm" /> : <span>✓</span>}
                  <span>Approve</span>
                </button>
                
                <button
                  onClick={() => handleModerationAction(selectedReport._id, "warn")}
                  disabled={isProcessing}
                  className="bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  {isProcessing ? <LoadingSpinner size="sm" /> : <span>⚠️</span>}
                  <span>Warn User</span>
                </button>
                
                <button
                  onClick={() => handleModerationAction(selectedReport._id, "remove")}
                  disabled={isProcessing}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  {isProcessing ? <LoadingSpinner size="sm" /> : <span>🗑️</span>}
                  <span>Remove Content</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

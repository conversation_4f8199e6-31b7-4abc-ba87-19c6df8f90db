// Admin Emergency Panel - Create and Manage Emergency Alerts
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { LoadingSpinner } from "../LoadingSpinner";
import { useToast } from "../Toast";

interface EmergencyAlertForm {
  title: { bn: string; en: string };
  message: { bn: string; en: string };
  severity: "low" | "medium" | "high" | "critical";
  category: "weather" | "safety" | "health" | "infrastructure" | "event" | "other";
  location?: {
    area: string;
    coordinates?: { lat: number; lng: number };
  };
  expiresAt?: number;
  actionRequired?: boolean;
  contactInfo?: {
    phone: string;
    emergency: boolean;
  };
}

export function AdminEmergencyPanel() {
  const { showToast } = useToast();
  const [activeTab, setActiveTab] = useState<"create" | "manage" | "stats">("create");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [form, setForm] = useState<EmergencyAlertForm>({
    title: { bn: "", en: "" },
    message: { bn: "", en: "" },
    severity: "medium",
    category: "other",
  });

  // Convex queries and mutations
  const activeAlerts = useQuery(api.core.emergency.getActiveAlerts, {});
  const alertStats = useQuery(api.core.emergency.getAlertStats, {});
  const createAlert = useMutation(api.core.emergency.createEmergencyAlert);
  const updateAlertStatus = useMutation(api.core.emergency.updateAlertStatus);
  const sendTestAlert = useMutation(api.core.emergency.sendTestAlert);

  const handleCreateAlert = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.title.bn || !form.message.bn) {
      showToast("Bengali title and message are required", "error");
      return;
    }

    setIsSubmitting(true);
    try {
      await createAlert({
        title: form.title,
        message: form.message,
        severity: form.severity,
        category: form.category,
        location: form.location,
        expiresAt: form.expiresAt,
        actionRequired: form.actionRequired,
        contactInfo: form.contactInfo,
      });
      
      showToast("Emergency alert created successfully!", "success");
      
      // Reset form
      setForm({
        title: { bn: "", en: "" },
        message: { bn: "", en: "" },
        severity: "medium",
        category: "other",
      });
    } catch (error) {
      showToast("Failed to create emergency alert", "error");
      console.error("Create alert error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateStatus = async (alertId: string, status: "active" | "resolved" | "cancelled", resolution?: string) => {
    try {
      await updateAlertStatus({ alertId, status, resolution });
      showToast(`Alert ${status} successfully`, "success");
    } catch (error) {
      showToast("Failed to update alert status", "error");
      console.error("Update status error:", error);
    }
  };

  const handleSendTestAlert = async () => {
    try {
      await sendTestAlert({ message: "This is a test emergency alert from the admin panel" });
      showToast("Test alert sent successfully", "success");
    } catch (error) {
      showToast("Failed to send test alert", "error");
      console.error("Test alert error:", error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-red-100 text-red-800 border-red-200";
      case "high": return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low": return "bg-blue-100 text-blue-800 border-blue-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: "create", label: "Create Alert", icon: "🚨" },
            { id: "manage", label: "Manage Alerts", icon: "📋" },
            { id: "stats", label: "Statistics", icon: "📊" },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? "border-red-500 text-red-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {/* Create Alert Tab */}
        {activeTab === "create" && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">Create Emergency Alert</h2>
              <button
                onClick={handleSendTestAlert}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Send Test Alert
              </button>
            </div>

            <form onSubmit={handleCreateAlert} className="space-y-6">
              {/* Title Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title (Bengali) *
                  </label>
                  <input
                    type="text"
                    value={form.title.bn}
                    onChange={(e) => setForm({ ...form, title: { ...form.title, bn: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="জরুরি সতর্কতার শিরোনাম"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title (English)
                  </label>
                  <input
                    type="text"
                    value={form.title.en}
                    onChange={(e) => setForm({ ...form, title: { ...form.title, en: e.target.value } })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Emergency alert title"
                  />
                </div>
              </div>

              {/* Message Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (Bengali) *
                  </label>
                  <textarea
                    value={form.message.bn}
                    onChange={(e) => setForm({ ...form, message: { ...form.message, bn: e.target.value } })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="বিস্তারিত বার্তা লিখুন..."
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (English)
                  </label>
                  <textarea
                    value={form.message.en}
                    onChange={(e) => setForm({ ...form, message: { ...form.message, en: e.target.value } })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Detailed message..."
                  />
                </div>
              </div>

              {/* Severity and Category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Severity Level
                  </label>
                  <select
                    value={form.severity}
                    onChange={(e) => setForm({ ...form, severity: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="low">Low - Information</option>
                    <option value="medium">Medium - Important</option>
                    <option value="high">High - Urgent</option>
                    <option value="critical">Critical - Emergency</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    value={form.category}
                    onChange={(e) => setForm({ ...form, category: e.target.value as any })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="weather">Weather</option>
                    <option value="safety">Safety</option>
                    <option value="health">Health</option>
                    <option value="infrastructure">Infrastructure</option>
                    <option value="event">Event</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              {/* Optional Fields */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="actionRequired"
                    checked={form.actionRequired || false}
                    onChange={(e) => setForm({ ...form, actionRequired: e.target.checked })}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <label htmlFor="actionRequired" className="text-sm font-medium text-gray-700">
                    Immediate action required
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location (Optional)
                  </label>
                  <input
                    type="text"
                    value={form.location?.area || ""}
                    onChange={(e) => setForm({ 
                      ...form, 
                      location: { ...form.location, area: e.target.value } 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="Specific area or location"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Phone (Optional)
                  </label>
                  <input
                    type="tel"
                    value={form.contactInfo?.phone || ""}
                    onChange={(e) => setForm({ 
                      ...form, 
                      contactInfo: { ...form.contactInfo, phone: e.target.value, emergency: form.contactInfo?.emergency || false } 
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="+8801XXXXXXXXX"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Creating...</span>
                    </>
                  ) : (
                    <>
                      <span>🚨</span>
                      <span>Create Emergency Alert</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Manage Alerts Tab */}
        {activeTab === "manage" && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Manage Active Alerts</h2>
            
            {activeAlerts === undefined ? (
              <LoadingSpinner text="Loading alerts..." />
            ) : activeAlerts?.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No active emergency alerts</p>
              </div>
            ) : (
              <div className="space-y-4">
                {activeAlerts?.map((alert: any) => (
                  <div key={alert._id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">{alert.title.en || alert.title.bn}</h3>
                        <p className="text-sm text-gray-600 mt-1">{alert.message.en || alert.message.bn}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(alert.severity)}`}>
                        {alert.severity.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        <span>Created: {new Date(alert.createdAt).toLocaleString()}</span>
                        <span className="ml-4">Acknowledged: {alert.acknowledgedBy.length}</span>
                      </div>
                      
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleUpdateStatus(alert._id, "resolved", "Issue resolved by admin")}
                          className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
                        >
                          Resolve
                        </button>
                        <button
                          onClick={() => handleUpdateStatus(alert._id, "cancelled", "Alert cancelled by admin")}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Statistics Tab */}
        {activeTab === "stats" && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-6">Emergency Alert Statistics</h2>
            
            {alertStats === undefined ? (
              <LoadingSpinner text="Loading statistics..." />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-blue-800">Total Alerts</h3>
                  <p className="text-2xl font-bold text-blue-900">{alertStats?.total || 0}</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-green-800">Active Alerts</h3>
                  <p className="text-2xl font-bold text-green-900">{alertStats?.active || 0}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-yellow-800">Resolved</h3>
                  <p className="text-2xl font-bold text-yellow-900">{alertStats?.resolved || 0}</p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-red-800">Critical Alerts</h3>
                  <p className="text-2xl font-bold text-red-900">{alertStats?.bySeverity?.critical || 0}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

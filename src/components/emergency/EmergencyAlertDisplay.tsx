// Emergency Alert Display Component - Critical Community Safety Feature
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState } from "react";
import { LoadingSpinner } from "../LoadingSpinner";
import { useToast } from "../Toast";

interface EmergencyAlert {
  _id: string;
  title: { bn: string; en: string };
  message: { bn: string; en: string };
  severity: "low" | "medium" | "high" | "critical";
  category: "weather" | "safety" | "health" | "infrastructure" | "event" | "other";
  status: "active" | "resolved" | "cancelled";
  acknowledgedBy: string[];
  createdAt: number;
  expiresAt?: number;
  actionRequired?: boolean;
  contactInfo?: {
    phone: string;
    emergency: boolean;
  };
}

export function EmergencyAlertDisplay() {
  const [language, setLanguage] = useState<"bn" | "en">("bn");
  const { showToast } = useToast();
  
  const emergencyAlerts = useQuery(api.core.emergency.getActiveAlerts, {});
  const acknowledgeAlert = useMutation(api.core.emergency.acknowledgeAlert);

  const handleAcknowledge = async (alertId: string) => {
    try {
      await acknowledgeAlert({ alertId });
      showToast("Alert acknowledged successfully", "success");
    } catch (error) {
      showToast("Failed to acknowledge alert", "error");
      console.error("Acknowledge error:", error);
    }
  };

  if (emergencyAlerts === undefined) {
    return (
      <div className="px-4 py-2">
        <LoadingSpinner size="sm" text="Loading emergency alerts..." />
      </div>
    );
  }

  if (!emergencyAlerts || emergencyAlerts.length === 0) {
    return null; // Don't show anything if no emergency alerts
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-red-600 border-red-700";
      case "high": return "bg-orange-500 border-orange-600";
      case "medium": return "bg-yellow-500 border-yellow-600";
      case "low": return "bg-blue-500 border-blue-600";
      default: return "bg-gray-500 border-gray-600";
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "critical": return "🚨";
      case "high": return "⚠️";
      case "medium": return "📢";
      case "low": return "ℹ️";
      default: return "📢";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "weather": return "🌦️";
      case "safety": return "🛡️";
      case "health": return "🏥";
      case "infrastructure": return "🏗️";
      case "event": return "📅";
      default: return "📢";
    }
  };

  return (
    <div className="px-4 py-2 space-y-3">
      {/* Language Toggle */}
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-bold text-gray-800">
          {language === "bn" ? "জরুরি সতর্কতা" : "Emergency Alerts"}
        </h2>
        <button
          onClick={() => setLanguage(language === "bn" ? "en" : "bn")}
          className="text-sm bg-gray-100 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
        >
          {language === "bn" ? "English" : "বাংলা"}
        </button>
      </div>

      {/* Emergency Alerts */}
      <div className="space-y-3">
        {emergencyAlerts.map((alert: EmergencyAlert) => (
          <div
            key={alert._id}
            className={`rounded-lg border-2 overflow-hidden shadow-lg ${getSeverityColor(alert.severity)}`}
          >
            {/* Alert Header */}
            <div className="bg-white bg-opacity-95 p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <div className="flex flex-col items-center space-y-1">
                    <span className="text-2xl">{getSeverityIcon(alert.severity)}</span>
                    <span className="text-lg">{getCategoryIcon(alert.category)}</span>
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-bold text-gray-900 text-lg mb-2">
                      {alert.title[language]}
                    </h3>
                    <p className="text-gray-700 text-sm leading-relaxed mb-3">
                      {alert.message[language]}
                    </p>
                    
                    {/* Alert Metadata */}
                    <div className="flex flex-wrap items-center gap-4 text-xs text-gray-600 mb-3">
                      <span className="flex items-center space-x-1">
                        <span>📅</span>
                        <span>{new Date(alert.createdAt).toLocaleString(language === "bn" ? "bn-BD" : "en-US")}</span>
                      </span>
                      
                      {alert.expiresAt && (
                        <span className="flex items-center space-x-1">
                          <span>⏰</span>
                          <span>
                            {language === "bn" ? "মেয়াদ শেষ: " : "Expires: "}
                            {new Date(alert.expiresAt).toLocaleString(language === "bn" ? "bn-BD" : "en-US")}
                          </span>
                        </span>
                      )}
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)} text-white`}>
                        {language === "bn" 
                          ? (alert.severity === "critical" ? "অত্যন্ত জরুরি" : 
                             alert.severity === "high" ? "জরুরি" :
                             alert.severity === "medium" ? "গুরুত্বপূর্ণ" : "তথ্য")
                          : alert.severity.toUpperCase()
                        }
                      </span>
                    </div>

                    {/* Contact Information */}
                    {alert.contactInfo && (
                      <div className="bg-gray-50 rounded-lg p-3 mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">📞</span>
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {language === "bn" ? "যোগাযোগ:" : "Contact:"}
                            </p>
                            <a 
                              href={`tel:${alert.contactInfo.phone}`}
                              className="text-blue-600 font-medium hover:underline"
                            >
                              {alert.contactInfo.phone}
                            </a>
                            {alert.contactInfo.emergency && (
                              <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                {language === "bn" ? "জরুরি" : "Emergency"}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Action Required Notice */}
                    {alert.actionRequired && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-yellow-600">⚡</span>
                          <p className="text-sm font-medium text-yellow-800">
                            {language === "bn" 
                              ? "অবিলম্বে ব্যবস্থা নিন" 
                              : "Immediate action required"
                            }
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Acknowledge Button */}
              <div className="flex justify-end mt-4">
                <button
                  onClick={() => handleAcknowledge(alert._id)}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <span>✓</span>
                  <span>{language === "bn" ? "স্বীকার করুন" : "Acknowledge"}</span>
                </button>
              </div>

              {/* Acknowledgment Count */}
              {alert.acknowledgedBy.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <p className="text-xs text-gray-600">
                    {language === "bn" 
                      ? `${alert.acknowledgedBy.length} জন স্বীকার করেছেন`
                      : `${alert.acknowledgedBy.length} people acknowledged`
                    }
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

import React, { Component, ErrorInfo, ReactNode } from "react";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'app' | 'page' | 'component';
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class EnhancedErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error(`[${this.props.level || 'component'}] Error:`, error, errorInfo);
    
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    if (import.meta.env.PROD) {
      console.error('Production error:', { error, errorInfo, level: this.props.level });
    }

    this.setState({ errorInfo });
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  private handleReload = () => {
    window.location.reload();
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const level = this.props.level || 'component';
      
      return (
        <div className={`flex items-center justify-center bg-gray-50 ${
          level === 'app' ? 'min-h-screen' : 'min-h-64'
        }`}>
          <div className="text-center p-6 max-w-md">
            <div className="text-4xl mb-4">
              {level === 'app' ? '💥' : level === 'page' ? '⚠️' : '🔧'}
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">
              {level === 'app' ? 'App Error' : level === 'page' ? 'Page Error' : 'Component Error'}
            </h2>
            <p className="text-gray-600 mb-4 text-sm">
              {level === 'app' 
                ? 'The application encountered an unexpected error'
                : level === 'page'
                ? 'This page failed to load properly'
                : 'This component failed to render'
              }
            </p>
            
            {import.meta.env.DEV && this.state.error && (
              <details className="text-left bg-red-50 p-3 rounded mb-4 text-xs">
                <summary className="cursor-pointer font-medium text-red-700">
                  Error Details
                </summary>
                <pre className="mt-2 text-red-600 overflow-auto">
                  {this.state.error.message}
                </pre>
              </details>
            )}

            <div className="space-y-2">
              {level !== 'app' && (
                <button
                  className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm"
                  onClick={this.handleRetry}
                >
                  Try Again
                </button>
              )}
              <button
                className="w-full bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 text-sm"
                onClick={this.handleReload}
              >
                Reload Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export const AppErrorBoundary = ({ children }: { children: ReactNode }) => (
  <EnhancedErrorBoundary level="app">{children}</EnhancedErrorBoundary>
);

export const PageErrorBoundary = ({ children }: { children: ReactNode }) => (
  <EnhancedErrorBoundary level="page">{children}</EnhancedErrorBoundary>
);

export const ComponentErrorBoundary = ({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) => (
  <EnhancedErrorBoundary level="component" fallback={fallback}>{children}</EnhancedErrorBoundary>
);

export { EnhancedErrorBoundary };

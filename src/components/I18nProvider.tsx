import React, { useState, useEffect, ReactNode } from 'react';
import { I18nContext, Language, getTranslation, isRTLLanguage, Translations } from '../i18n';

interface I18nProviderProps {
  children: ReactNode;
  defaultLanguage?: Language;
}

export const I18nProvider = ({ children, defaultLanguage = 'bn' }: I18nProviderProps) => {
  const [language, setLanguage] = useState<Language>(() => {
    // Try to get language from localStorage or use default
    const saved = localStorage.getItem('smart-village-language');
    return (saved as Language) || defaultLanguage;
  });

  const [isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    // Update RTL state when language changes
    setIsRTL(isRTLLanguage(language));
    
    // Save language preference
    localStorage.setItem('smart-village-language', language);
    
    // Update document direction and language
    document.documentElement.lang = language;
    document.documentElement.dir = isRTLLanguage(language) ? 'rtl' : 'ltr';
  }, [language]);

  const t = (key: keyof Translations): string => {
    return getTranslation(language, key);
  };

  const contextValue = {
    language,
    setLanguage,
    t,
    isRTL
  };

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'white' | 'gray';
  text?: string;
  fullScreen?: boolean;
}

export const LoadingSpinner = ({ 
  size = 'md', 
  color = 'blue', 
  text,
  fullScreen = false 
}: LoadingSpinnerProps) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-12 w-12';
      default:
        return 'h-8 w-8';
    }
  };

  const getColorClasses = () => {
    switch (color) {
      case 'white':
        return 'border-white border-t-transparent';
      case 'gray':
        return 'border-gray-300 border-t-transparent';
      default:
        return 'border-blue-500 border-t-transparent';
    }
  };

  const spinner = (
    <div className={`animate-spin rounded-full border-2 ${getSizeClasses()} ${getColorClasses()}`} />
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        <div className="text-center">
          {spinner}
          {text && <p className="mt-4 text-gray-600">{text}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center">
      {spinner}
      {text && <span className="ml-2 text-gray-600">{text}</span>}
    </div>
  );
};

export const InlineLoader = ({ text = "Loading..." }: { text?: string }) => (
  <LoadingSpinner size="sm" text={text} />
);

export const PageLoader = ({ text = "Loading page..." }: { text?: string }) => (
  <div className="flex items-center justify-center min-h-64">
    <LoadingSpinner size="lg" text={text} />
  </div>
);

export const FullScreenLoader = ({ text = "Loading..." }: { text?: string }) => (
  <LoadingSpinner fullScreen text={text} />
);

import React, { useState, useEffect } from 'react';
import { useI18n } from '../i18n';

interface SMSAuthProps {
  onSuccess: (phoneNumber: string, verificationCode: string) => void;
  onError: (error: string) => void;
}

export const SMSAuth = ({ onSuccess, onError }: SMSAuthProps) => {
  const { t } = useI18n();
  const [step, setStep] = useState<'phone' | 'verify'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const sendSMS = async () => {
    if (!phoneNumber || phoneNumber.length < 11) {
      onError('Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate SMS sending
      // In real implementation, integrate with SMS gateway like SSL Wireless, Grameenphone, etc.
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setStep('verify');
      setCountdown(60); // 60 second countdown
      
      // For demo purposes, show the verification code
      console.log('Demo verification code: 123456');
    } catch (error) {
      onError('Failed to send SMS. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      onError('Please enter the 6-digit verification code');
      return;
    }

    setIsLoading(true);
    try {
      // Simulate verification
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo, accept 123456 as valid code
      if (verificationCode === '123456') {
        onSuccess(phoneNumber, verificationCode);
      } else {
        onError('Invalid verification code. Please try again.');
      }
    } catch (error) {
      onError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const resendSMS = () => {
    if (countdown === 0) {
      sendSMS();
    }
  };

  if (step === 'phone') {
    return (
      <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
        <div className="text-center mb-6">
          <div className="text-4xl mb-2">📱</div>
          <h2 className="text-xl font-semibold">SMS Verification</h2>
          <p className="text-gray-600 text-sm mt-2">
            Enter your phone number to receive a verification code
          </p>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">
            {t('phoneNumber')}
          </label>
          <div className="flex">
            <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-md">
              +880
            </span>
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              placeholder="1XXXXXXXXX"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxLength={10}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Enter your 11-digit mobile number without country code
          </p>
        </div>

        <button
          onClick={sendSMS}
          disabled={isLoading || !phoneNumber}
          className="w-full py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Sending...' : 'Send Verification Code'}
        </button>

        <div className="mt-4 text-xs text-gray-500 text-center">
          <p>Standard SMS rates may apply</p>
          <p>We'll send you a 6-digit verification code</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
      <div className="text-center mb-6">
        <div className="text-4xl mb-2">🔐</div>
        <h2 className="text-xl font-semibold">Enter Verification Code</h2>
        <p className="text-gray-600 text-sm mt-2">
          We sent a 6-digit code to +880{phoneNumber}
        </p>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Verification Code
        </label>
        <input
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
          placeholder="123456"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-center text-lg tracking-widest"
          maxLength={6}
        />
      </div>

      <button
        onClick={verifyCode}
        disabled={isLoading || verificationCode.length !== 6}
        className="w-full py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed mb-3"
      >
        {isLoading ? 'Verifying...' : 'Verify Code'}
      </button>

      <div className="text-center">
        <button
          onClick={resendSMS}
          disabled={countdown > 0 || isLoading}
          className="text-blue-500 text-sm hover:underline disabled:text-gray-400 disabled:no-underline"
        >
          {countdown > 0 ? `Resend in ${countdown}s` : 'Resend Code'}
        </button>
      </div>

      <button
        onClick={() => setStep('phone')}
        className="w-full mt-3 py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
      >
        Change Phone Number
      </button>

      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Demo code: 123456</p>
      </div>
    </div>
  );
};

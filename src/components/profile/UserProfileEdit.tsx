// User Profile Edit Component - Allow users to edit their profiles
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState, useEffect } from "react";
import { LoadingSpinner } from "../LoadingSpinner";
import { useToast } from "../Toast";
import { SafeInput } from "../SafeInput";

interface UserProfile {
  _id: string;
  identity: {
    name: {
      bn: string;
      en?: string;
    };
  };
  contact: {
    phone?: string;
  };
  preferences: {
    language: "bn" | "en";
    theme: "light" | "dark" | "auto";
  };
  role: "user" | "business_owner" | "admin";
  isOnboarded: boolean;
}

export function UserProfileEdit() {
  const { showToast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [language, setLanguage] = useState<"bn" | "en">("bn");
  
  // Form state
  const [formData, setFormData] = useState({
    name: { bn: "", en: "" },
    phone: "",
    language: "bn" as "bn" | "en",
    theme: "light" as "light" | "dark" | "auto",
  });

  // Convex queries and mutations
  const userProfile = useQuery(api.core.users.getCurrent);
  const updateProfile = useMutation(api.core.users.update);

  // Initialize form data when profile loads
  useEffect(() => {
    if (userProfile) {
      setFormData({
        name: {
          bn: userProfile.identity.name.bn || "",
          en: userProfile.identity.name.en || "",
        },
        phone: userProfile.contact.phone || "",
        language: userProfile.preferences.language || "bn",
        theme: userProfile.preferences.theme || "light",
      });
      setLanguage(userProfile.preferences.language || "bn");
    }
  }, [userProfile]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.bn.trim()) {
      showToast(
        language === "bn" ? "বাংলা নাম প্রয়োজনীয়" : "Bengali name is required",
        "error"
      );
      return;
    }

    setIsSaving(true);
    try {
      await updateProfile({
        updates: {
          name: {
            bn: formData.name.bn.trim(),
            en: formData.name.en.trim() || undefined,
          },
          phone: formData.phone.trim() || undefined,
          preferences: {
            language: formData.language,
            theme: formData.theme,
          },
        },
      });
      
      showToast(
        language === "bn" 
          ? "প্রোফাইল সফলভাবে আপডেট হয়েছে"
          : "Profile updated successfully",
        "success"
      );
      
      setIsEditing(false);
    } catch (error) {
      showToast(
        language === "bn" 
          ? "প্রোফাইল আপডেট করতে ব্যর্থ"
          : "Failed to update profile",
        "error"
      );
      console.error("Profile update error:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (userProfile) {
      setFormData({
        name: {
          bn: userProfile.identity.name.bn || "",
          en: userProfile.identity.name.en || "",
        },
        phone: userProfile.contact.phone || "",
        language: userProfile.preferences.language || "bn",
        theme: userProfile.preferences.theme || "light",
      });
    }
    setIsEditing(false);
  };

  if (userProfile === undefined) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <LoadingSpinner text="Loading profile..." />
      </div>
    );
  }

  if (!userProfile) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="text-center py-8">
          <span className="text-6xl mb-4 block">👤</span>
          <p className="text-gray-500">Profile not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">👤</span>
          <div>
            <h2 className="text-lg font-bold text-gray-900">
              {language === "bn" ? "ব্যবহারকারী প্রোফাইল" : "User Profile"}
            </h2>
            <p className="text-sm text-gray-600">
              {language === "bn" 
                ? "আপনার ব্যক্তিগত তথ্য পরিচালনা করুন"
                : "Manage your personal information"
              }
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Language Toggle */}
          <button
            onClick={() => setLanguage(language === "bn" ? "en" : "bn")}
            className="text-sm bg-gray-100 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
          >
            {language === "bn" ? "English" : "বাংলা"}
          </button>
          
          {/* Edit Button */}
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
            >
              <span>✏️</span>
              <span>{language === "bn" ? "সম্পাদনা" : "Edit"}</span>
            </button>
          )}
        </div>
      </div>

      <div className="p-6">
        {isEditing ? (
          /* Edit Form */
          <form onSubmit={handleSave} className="space-y-6">
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === "bn" ? "নাম (বাংলা) *" : "Name (Bengali) *"}
                </label>
                <SafeInput
                  type="name"
                  value={formData.name.bn}
                  onChange={(value) => setFormData({ ...formData, name: { ...formData.name, bn: value } })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={language === "bn" ? "আপনার বাংলা নাম" : "Your Bengali name"}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === "bn" ? "নাম (ইংরেজি)" : "Name (English)"}
                </label>
                <SafeInput
                  type="name"
                  value={formData.name.en}
                  onChange={(value) => setFormData({ ...formData, name: { ...formData.name, en: value } })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder={language === "bn" ? "আপনার ইংরেজি নাম" : "Your English name"}
                />
              </div>
            </div>

            {/* Phone Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {language === "bn" ? "ফোন নম্বর" : "Phone Number"}
              </label>
              <SafeInput
                type="phone"
                value={formData.phone}
                onChange={(value) => setFormData({ ...formData, phone: value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="+8801XXXXXXXXX"
              />
            </div>

            {/* Preferences */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === "bn" ? "ভাষা" : "Language"}
                </label>
                <select
                  value={formData.language}
                  onChange={(e) => setFormData({ ...formData, language: e.target.value as "bn" | "en" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="bn">{language === "bn" ? "বাংলা" : "Bengali"}</option>
                  <option value="en">{language === "bn" ? "ইংরেজি" : "English"}</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {language === "bn" ? "থিম" : "Theme"}
                </label>
                <select
                  value={formData.theme}
                  onChange={(e) => setFormData({ ...formData, theme: e.target.value as "light" | "dark" | "auto" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="light">{language === "bn" ? "হালকা" : "Light"}</option>
                  <option value="dark">{language === "bn" ? "গাঢ়" : "Dark"}</option>
                  <option value="auto">{language === "bn" ? "স্বয়ংক্রিয়" : "Auto"}</option>
                </select>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancel}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
              >
                {language === "bn" ? "বাতিল" : "Cancel"}
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
              >
                {isSaving ? (
                  <>
                    <LoadingSpinner size="sm" />
                    <span>{language === "bn" ? "সংরক্ষণ করা হচ্ছে..." : "Saving..."}</span>
                  </>
                ) : (
                  <>
                    <span>💾</span>
                    <span>{language === "bn" ? "সংরক্ষণ করুন" : "Save Changes"}</span>
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          /* Display Mode */
          <div className="space-y-6">
            {/* Profile Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">
                    {language === "bn" ? "নাম (বাংলা)" : "Name (Bengali)"}
                  </h3>
                  <p className="text-lg text-gray-900">{userProfile.identity.name.bn}</p>
                </div>
                
                {userProfile.identity.name.en && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-1">
                      {language === "bn" ? "নাম (ইংরেজি)" : "Name (English)"}
                    </h3>
                    <p className="text-lg text-gray-900">{userProfile.identity.name.en}</p>
                  </div>
                )}
                
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">
                    {language === "bn" ? "ফোন নম্বর" : "Phone Number"}
                  </h3>
                  <p className="text-lg text-gray-900">
                    {userProfile.contact.phone || (language === "bn" ? "যোগ করা হয়নি" : "Not added")}
                  </p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">
                    {language === "bn" ? "ভূমিকা" : "Role"}
                  </h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                    userProfile.role === "admin" 
                      ? "bg-red-100 text-red-800"
                      : userProfile.role === "business_owner"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-green-100 text-green-800"
                  }`}>
                    {userProfile.role === "admin" 
                      ? (language === "bn" ? "প্রশাসক" : "Admin")
                      : userProfile.role === "business_owner"
                      ? (language === "bn" ? "ব্যবসায়ী" : "Business Owner")
                      : (language === "bn" ? "ব্যবহারকারী" : "User")
                    }
                  </span>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">
                    {language === "bn" ? "ভাষা" : "Language"}
                  </h3>
                  <p className="text-lg text-gray-900">
                    {userProfile.preferences.language === "bn" 
                      ? (language === "bn" ? "বাংলা" : "Bengali")
                      : (language === "bn" ? "ইংরেজি" : "English")
                    }
                  </p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-1">
                    {language === "bn" ? "থিম" : "Theme"}
                  </h3>
                  <p className="text-lg text-gray-900 capitalize">
                    {userProfile.preferences.theme === "light"
                      ? (language === "bn" ? "হালকা" : "Light")
                      : userProfile.preferences.theme === "dark"
                      ? (language === "bn" ? "গাঢ়" : "Dark")
                      : (language === "bn" ? "স্বয়ংক্রিয়" : "Auto")
                    }
                  </p>
                </div>
              </div>
            </div>

            {/* Account Status */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                {language === "bn" ? "অ্যাকাউন্টের অবস্থা" : "Account Status"}
              </h3>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${userProfile.isOnboarded ? "bg-green-500" : "bg-yellow-500"}`}></span>
                <span className="text-sm text-gray-600">
                  {userProfile.isOnboarded 
                    ? (language === "bn" ? "সম্পূর্ণ প্রোফাইল" : "Complete Profile")
                    : (language === "bn" ? "অসম্পূর্ণ প্রোফাইল" : "Incomplete Profile")
                  }
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

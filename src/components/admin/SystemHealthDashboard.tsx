// System Health Dashboard - Monitor overall system status
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { LoadingSpinner } from "../LoadingSpinner";

export function SystemHealthDashboard() {
  const systemHealth = useQuery(api.core.systemTest.getSystemHealth, {});

  if (systemHealth === undefined) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <LoadingSpinner text="Loading system health..." />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "bg-green-100 text-green-800 border-green-200";
      case "warning": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "error": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy": return "✅";
      case "warning": return "⚠️";
      case "error": return "❌";
      default: return "❓";
    }
  };

  const componentLabels = {
    database: "Database",
    authentication: "Authentication",
    homescreen: "HomeScreen Data",
    moderation: "Content Moderation",
    emergency: "Emergency Alerts",
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900">System Health Dashboard</h2>
        <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(systemHealth.overall)}`}>
          <span className="mr-2">{getStatusIcon(systemHealth.overall)}</span>
          Overall: {systemHealth.overall.toUpperCase()}
        </div>
      </div>

      {/* Component Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {Object.entries(systemHealth.components).map(([component, status]) => (
          <div key={component} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-gray-900">
                {componentLabels[component as keyof typeof componentLabels] || component}
              </h3>
              <span className="text-xl">{getStatusIcon(status)}</span>
            </div>
            <div className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(status)}`}>
              {status.toUpperCase()}
            </div>
          </div>
        ))}
      </div>

      {/* System Statistics */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-800 mb-1">Total Users</div>
            <div className="text-2xl font-bold text-blue-900">{systemHealth.stats.totalUsers}</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-green-800 mb-1">Active Alerts</div>
            <div className="text-2xl font-bold text-green-900">{systemHealth.stats.activeAlerts}</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="text-sm text-yellow-800 mb-1">Pending Reports</div>
            <div className="text-2xl font-bold text-yellow-900">{systemHealth.stats.pendingReports}</div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <div className="text-sm text-red-800 mb-1">Emergency Alerts</div>
            <div className="text-2xl font-bold text-red-900">{systemHealth.stats.emergencyAlerts}</div>
          </div>
        </div>
      </div>

      {/* System Status Summary */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-2">Status Summary</h4>
        <div className="text-sm text-gray-600">
          {systemHealth.overall === "healthy" && (
            <p>✅ All systems are operating normally. The Smart Village app is ready for production use.</p>
          )}
          {systemHealth.overall === "warning" && (
            <p>⚠️ Some components have warnings but the system is still operational. Monitor closely.</p>
          )}
          {systemHealth.overall === "error" && (
            <p>❌ Critical system errors detected. Immediate attention required.</p>
          )}
        </div>
      </div>

      {/* Last Updated */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        Last updated: {new Date().toLocaleString()}
      </div>
    </div>
  );
}

import React, { useState } from 'react';
import { useI18n } from '../i18n';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  type: 'mobile' | 'bank' | 'card';
  available: boolean;
}

const BANGLADESH_PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'bkash',
    name: 'bKash',
    icon: '💳',
    type: 'mobile',
    available: true
  },
  {
    id: 'nagad',
    name: 'Nagad',
    icon: '📱',
    type: 'mobile',
    available: true
  },
  {
    id: 'rocket',
    name: 'Rocket',
    icon: '🚀',
    type: 'mobile',
    available: true
  },
  {
    id: 'upay',
    name: 'Upay',
    icon: '💰',
    type: 'mobile',
    available: true
  },
  {
    id: 'bank',
    name: 'Bank Transfer',
    icon: '🏦',
    type: 'bank',
    available: false // Not implemented yet
  }
];

interface PaymentIntegrationProps {
  amount: number;
  purpose: string;
  onSuccess: (transactionId: string) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export const PaymentIntegration = ({
  amount,
  purpose,
  onSuccess,
  onError,
  onCancel
}: PaymentIntegrationProps) => {
  const { t } = useI18n();
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');

  const handlePayment = async () => {
    if (!selectedMethod || !phoneNumber) {
      onError('Please select payment method and enter phone number');
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate payment processing
      // In real implementation, integrate with actual payment gateways
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success
      const transactionId = `TXN${Date.now()}`;
      onSuccess(transactionId);
    } catch (error) {
      onError('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
      <h3 className="text-lg font-semibold mb-4">Payment Details</h3>
      
      <div className="mb-4">
        <p className="text-sm text-gray-600">Amount</p>
        <p className="text-2xl font-bold text-green-600">৳{amount}</p>
        <p className="text-sm text-gray-500">{purpose}</p>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Select Payment Method
        </label>
        <div className="grid grid-cols-2 gap-2">
          {BANGLADESH_PAYMENT_METHODS.map((method) => (
            <button
              key={method.id}
              onClick={() => setSelectedMethod(method.id)}
              disabled={!method.available}
              className={`p-3 border rounded-lg text-center ${
                selectedMethod === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              } ${
                !method.available
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:border-blue-300'
              }`}
            >
              <div className="text-2xl mb-1">{method.icon}</div>
              <div className="text-sm font-medium">{method.name}</div>
              {!method.available && (
                <div className="text-xs text-gray-400">Coming Soon</div>
              )}
            </button>
          ))}
        </div>
      </div>

      {selectedMethod && (
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">
            Phone Number
          </label>
          <input
            type="tel"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            placeholder="01XXXXXXXXX"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}

      <div className="flex space-x-3">
        <button
          onClick={onCancel}
          className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          {t('cancel')}
        </button>
        <button
          onClick={handlePayment}
          disabled={!selectedMethod || !phoneNumber || isProcessing}
          className="flex-1 py-2 px-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isProcessing ? 'Processing...' : 'Pay Now'}
        </button>
      </div>

      <div className="mt-4 text-xs text-gray-500 text-center">
        <p>Secure payment powered by local payment gateways</p>
        <p>Your payment information is encrypted and secure</p>
      </div>
    </div>
  );
};

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { ClerkProvider, useAuth } from "@clerk/clerk-react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { ConvexReactClient } from "convex/react";
import "./index.css";
import App from "./App.tsx";
import { ErrorBoundary } from "./ErrorBoundary.tsx";
import { AppErrorBoundary } from "./components/EnhancedErrorBoundary.tsx";
import { ToastProvider } from "./components/Toast.tsx";
import { env } from "./config/environment";

const convex = new ConvexReactClient(env.convexUrl);
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AppErrorBoundary>
      <ToastProvider>
        <ClerkProvider
          publishableKey={env.clerkPublishableKey}
        >
          <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
            <App />
          </ConvexProviderWithClerk>
        </ClerkProvider>
      </ToastProvider>
    </AppErrorBoundary>
  </StrictMode>,
);

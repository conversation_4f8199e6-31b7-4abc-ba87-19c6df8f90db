// src/App.tsx
"use client";

import { Authenticated, Unauthenticated, useMutation } from "convex/react";
import { SignInButton, UserButton, useUser, useClerk } from "@clerk/clerk-react";
import { useEffect } from "react";
// Import the correct API definition generated by Convex
import { api } from "../convex/_generated/api";
import { lazy, Suspense } from "react";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { PageLoader } from "./components/LoadingSpinner";

// Lazy load components for better performance
const HomeScreen = lazy(() => import("./core/HomeScreen"));
const AdminPanel = lazy(() => import("./core/AdminPanel"));
const UserManagementPanel = lazy(() => import("./core/UserManagementPanel"));

export default function App() {
  return (
    <>
      <main>
        <Unauthenticated>
          <SignInView />
        </Unauthenticated>
        <Authenticated>
          <AuthenticatedApp />
        </Authenticated>
      </main>
    </>
  );
}

function AuthenticatedApp() {
  const { user, isLoaded } = useUser();
  const createOnSignUp = useMutation(api.core.users.createOnSignUp);

  useEffect(() => {
    // Only create user if we have authentication info and it's loaded
    if (isLoaded && user) {
      console.log("User authenticated:", user.id); // Debug log
      
      // Determine user name with better fallbacks
      const userName = user.fullName || 
                      (user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : null) ||
                      user.emailAddresses?.[0]?.emailAddress ||
                      "নতুন ব্যবহারকারী"; // "New User" in Bengali
      
      console.log("Creating user with name:", userName); // Debug log
      
      // This mutation is idempotent, so it's safe to call on every login.
      // We pass more meaningful initial values for better user experience.
      void createOnSignUp({ 
        name: { 
          bn: userName,
          en: userName 
        }, 
        phone: undefined // Allow user to add phone during onboarding
      });
    }
  }, [user, isLoaded, createOnSignUp]);

  return (
    <ProtectedRoute>
      <Suspense fallback={<PageLoader text="Loading app..." />}>
        <HomeScreen />
      </Suspense>
    </ProtectedRoute>
  );
}

function SignInView() {
  const { isLoaded } = useUser();
  const { signOut } = useClerk();

  // Don't render anything while checking authentication state
  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="w-full max-w-sm">
        {/* Logo/Header */}
        <div className="text-center mb-8">
          <div className="text-4xl mb-4">🏘️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Smart Village</h1>
          <p className="text-gray-600">Your hyperlocal community app</p>
        </div>

        {/* Sign In Card */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-xl font-semibold text-center mb-6">Welcome Back</h2>

          <div className="space-y-4">
            <SignInButton mode="modal">
              <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2">
                <span>📱</span>
                <span>Sign in with Phone</span>
              </button>
            </SignInButton>

            <div className="text-center text-sm text-gray-500">
              New to Smart Village? Sign up when you sign in
            </div>

            <button
              onClick={() => void signOut()}
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-xl text-sm hover:bg-gray-200 transition-colors"
            >
              Clear Session (if stuck)
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 text-xs text-gray-500">
          <p>Connecting communities across Bangladesh</p>
        </div>
      </div>
    </div>
  );
}
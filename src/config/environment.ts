// src/config/environment.ts
// Secure environment configuration with validation

interface EnvironmentConfig {
  convexUrl: string;
  clerkPublishableKey: string;
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
}

// Validate required environment variables
function validateEnvironment(): EnvironmentConfig {
  const convexUrl = import.meta.env.VITE_CONVEX_URL;
  const clerkPublishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;
  const mode = import.meta.env.MODE;

  // Validate required variables
  if (!convexUrl) {
    throw new Error("VITE_CONVEX_URL is required but not set");
  }

  if (!clerkPublishableKey) {
    throw new Error("VITE_CLERK_PUBLISHABLE_KEY is required but not set");
  }

  // Validate Clerk key format (should start with pk_)
  if (!clerkPublishableKey.startsWith('pk_')) {
    throw new Error("VITE_CLERK_PUBLISHABLE_KEY must be a valid Clerk publishable key (starts with 'pk_')");
  }

  // Validate Convex URL format
  if (!convexUrl.match(/^https?:\/\/.+/)) {
    throw new Error("VITE_CONVEX_URL must be a valid URL");
  }

  // Warn about development keys in production
  if (mode === 'production' && clerkPublishableKey.includes('test')) {
    console.warn("⚠️ WARNING: Using test Clerk keys in production mode");
  }

  return {
    convexUrl,
    clerkPublishableKey,
    isDevelopment: mode === 'development',
    isProduction: mode === 'production',
    isTest: mode === 'test',
  };
}

// Export validated configuration
export const env = validateEnvironment();

// Security utilities
export const security = {
  // Check if we're in a secure context (HTTPS or localhost)
  isSecureContext: () => {
    return window.location.protocol === 'https:' || 
           window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1';
  },

  // Sanitize sensitive data for logging
  sanitizeForLogging: (data: any) => {
    const sanitized = { ...data };
    
    // Remove or mask sensitive fields
    const sensitiveFields = ['password', 'token', 'key', 'secret', 'auth'];
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      }
    });
    
    return sanitized;
  },

  // Generate a simple hash for client-side data integrity
  simpleHash: (str: string) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }
};

// Development helpers
export const dev = {
  // Log environment info in development
  logEnvironmentInfo: () => {
    if (env.isDevelopment) {
      console.log('🔧 Environment Configuration:', {
        mode: import.meta.env.MODE,
        convexUrl: env.convexUrl,
        clerkKey: env.clerkPublishableKey.substring(0, 10) + '...',
        isSecure: security.isSecureContext(),
      });
    }
  },

  // Check for common development issues
  checkDevelopmentIssues: () => {
    if (env.isDevelopment) {
      // Check for localhost vs 127.0.0.1 consistency
      if (env.convexUrl.includes('127.0.0.1') && window.location.hostname === 'localhost') {
        console.warn('⚠️ Convex URL uses 127.0.0.1 but accessing via localhost. This may cause CORS issues.');
      }

      // Check for HTTPS in development
      if (!security.isSecureContext() && window.location.hostname !== 'localhost') {
        console.warn('⚠️ Not in secure context. Some features may not work properly.');
      }
    }
  }
};

// Initialize development checks
if (env.isDevelopment) {
  dev.logEnvironmentInfo();
  dev.checkDevelopmentIssues();
}

// src/features/directory/DirectorySearchPage.tsx
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { useState, useCallback } from "react";

export function DirectorySearchPage() {
  const [searchParams, setSearchParams] = useState({
    searchQuery: "",
    categoryId: undefined as string | undefined,
    locationId: undefined as string | undefined,
    sortBy: "newest" as "newest" | "featured" | "name",
  });

  const { data: results, isLoading } = useQuery(
    api.directory.search.searchListings,
    searchParams
  );

  console.log("DirectorySearchPage rendering with results:", results);
  console.log("DirectorySearchPage loading state:", isLoading);
  console.log("DirectorySearchPage searchParams:", searchParams);

  const handleSearch = useCallback((newParams: Partial<typeof searchParams>) => {
    console.log("DirectorySearchPage handleSearch with newParams:", newParams);
    setSearchParams(prev => ({ ...prev, ...newParams }));
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch({ searchQuery: e.target.value });
  }, [handleSearch]);

  // Get categories and locations for filters
  const { data: categories } = useQuery(api.directory.categories.listCategories);
  const { data: locations } = useQuery(api.core.locations.list);

  console.log("Categories:", categories);
  console.log("Locations:", locations);

  return (
    <div className="directory-search-page p-6">
      <h1 className="text-2xl font-bold mb-6">Business Directory</h1>
      
      <div className="search-filters mb-6 p-4 bg-white rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <input
              type="text"
              placeholder="Search businesses..."
              value={searchParams.searchQuery}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
          
          <div>
            <select
              value={searchParams.categoryId || ""}
              onChange={(e) => handleSearch({ categoryId: e.target.value || undefined })}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">All Categories</option>
              {categories?.map((category: any) => (
                <option key={category._id} value={category._id}>
                  {category.name.bn} {category.name.en && `(${category.name.en})`}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select
              value={searchParams.locationId || ""}
              onChange={(e) => handleSearch({ locationId: e.target.value || undefined })}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="">All Locations</option>
              {locations?.map((location: any) => (
                <option key={location._id} value={location._id}>
                  {location.name.bn} {location.name.en && `(${location.name.en})`}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select
              value={searchParams.sortBy}
              onChange={(e) => handleSearch({ sortBy: e.target.value as any })}
              className="w-full p-2 border border-gray-300 rounded"
            >
              <option value="newest">Newest First</option>
              <option value="featured">Featured</option>
              <option value="name">Name (A-Z)</option>
            </select>
          </div>
        </div>
      </div>

      <div className="results-info mb-4">
        <h2 className="text-xl font-semibold">
          {results ? `${results.items.length} businesses found` : "Searching..."}
        </h2>
      </div>

      {isLoading && !results ? (
        <div className="text-center py-12">Loading...</div>
      ) : results && results.items.length > 0 ? (
        <div className="results-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {results.items.map((listing: any) => (
            <DirectoryListingCard key={listing._id} listingId={listing._id} />
          ))}
        </div>
      ) : (
        <div className="empty-state text-center py-12">
          <h3 className="text-xl font-semibold mb-2">No businesses found</h3>
          <p className="text-gray-600">Try adjusting your search filters</p>
          <div className="mt-4">
            <p className="text-sm text-gray-500">
              Debug info: {categories?.length || 0} categories, {locations?.length || 0} locations
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

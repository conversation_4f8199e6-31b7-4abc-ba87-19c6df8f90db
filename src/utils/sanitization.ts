// src/utils/sanitization.ts
// Input sanitization utilities for user-generated content

/**
 * Basic HTML sanitization to prevent XSS attacks
 * Removes potentially dangerous HTML tags and attributes
 */
export function sanitizeHtml(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Remove script tags and their content
  let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove dangerous HTML tags
  const dangerousTags = [
    'script', 'iframe', 'object', 'embed', 'form', 'input', 'button',
    'textarea', 'select', 'option', 'link', 'meta', 'style'
  ];
  
  dangerousTags.forEach(tag => {
    const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
    sanitized = sanitized.replace(regex, '');
    
    // Also remove self-closing tags
    const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*\\/>`, 'gi');
    sanitized = sanitized.replace(selfClosingRegex, '');
  });

  // Remove dangerous attributes
  const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'];
  dangerousAttrs.forEach(attr => {
    const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi');
    sanitized = sanitized.replace(regex, '');
  });

  // Remove javascript: URLs
  sanitized = sanitized.replace(/javascript:/gi, '');
  
  return sanitized.trim();
}

/**
 * Sanitize text input by removing/escaping dangerous characters
 */
export function sanitizeText(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Sanitize phone number input
 */
export function sanitizePhone(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Keep only digits, plus sign, spaces, and hyphens
  return input.replace(/[^\d+\s-]/g, '').trim();
}

/**
 * Sanitize name input (Bengali and English)
 */
export function sanitizeName(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Allow letters (including Bengali), spaces, and common punctuation
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/[^\p{L}\p{N}\s\-'.]/gu, '') // Keep letters, numbers, spaces, hyphens, apostrophes, dots
    .replace(/\s+/g, ' ') // Normalize spaces
    .trim();
}

/**
 * Sanitize search query
 */
export function sanitizeSearchQuery(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: URLs
    .replace(/[^\p{L}\p{N}\s\-'.]/gu, '') // Keep letters, numbers, spaces, basic punctuation
    .replace(/\s+/g, ' ') // Normalize spaces
    .trim()
    .substring(0, 100); // Limit length
}

/**
 * Validate and sanitize URL input
 */
export function sanitizeUrl(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Remove dangerous protocols
  const sanitized = input.replace(/^(javascript|data|vbscript):/gi, '');
  
  // Ensure it starts with http:// or https:// if it looks like a URL
  if (sanitized.includes('.') && !sanitized.match(/^https?:\/\//)) {
    return `https://${sanitized}`;
  }
  
  return sanitized;
}

/**
 * Comprehensive input sanitization based on input type
 */
export function sanitizeInput(input: string, type: 'text' | 'html' | 'phone' | 'name' | 'search' | 'url' = 'text'): string {
  switch (type) {
    case 'html':
      return sanitizeHtml(input);
    case 'phone':
      return sanitizePhone(input);
    case 'name':
      return sanitizeName(input);
    case 'search':
      return sanitizeSearchQuery(input);
    case 'url':
      return sanitizeUrl(input);
    case 'text':
    default:
      return sanitizeText(input);
  }
}

/**
 * Validation utilities
 */
export const validation = {
  // Check if input contains potentially dangerous content
  isDangerous: (input: string): boolean => {
    if (!input || typeof input !== 'string') {
      return false;
    }

    const dangerousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /eval\s*\(/i,
      /document\./i,
      /window\./i,
    ];

    return dangerousPatterns.some(pattern => pattern.test(input));
  },

  // Check if input is too long
  isTooLong: (input: string, maxLength: number = 1000): boolean => {
    return input && input.length > maxLength;
  },

  // Check if input contains only allowed characters for names
  isValidName: (input: string): boolean => {
    if (!input || typeof input !== 'string') {
      return false;
    }
    
    // Allow letters (including Bengali), spaces, hyphens, apostrophes, dots
    return /^[\p{L}\p{N}\s\-'.]+$/u.test(input) && input.length <= 100;
  },

  // Check if phone number format is valid
  isValidPhone: (input: string): boolean => {
    if (!input || typeof input !== 'string') {
      return false;
    }
    
    // Basic international phone number validation
    const cleaned = input.replace(/[\s-]/g, '');
    return /^\+?[1-9]\d{7,14}$/.test(cleaned);
  }
};

/**
 * React hook for input sanitization
 */
export function useSanitizedInput(initialValue: string = '', type: 'text' | 'html' | 'phone' | 'name' | 'search' | 'url' = 'text') {
  const [value, setValue] = useState(sanitizeInput(initialValue, type));
  
  const updateValue = (newValue: string) => {
    const sanitized = sanitizeInput(newValue, type);
    setValue(sanitized);
    return sanitized;
  };

  return [value, updateValue] as const;
}

// Import useState for the hook
import { useState } from 'react';

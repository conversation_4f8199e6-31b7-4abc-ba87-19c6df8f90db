# Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# Convex Configuration
CONVEX_DEPLOYMENT=your-deployment-name
VITE_CONVEX_URL=https://your-convex-deployment.convex.cloud

# Clerk Authentication
VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_clerk_publishable_key
CLERK_JWT_ISSUER_DOMAIN=https://your-clerk-domain.clerk.accounts.dev

# Development Only (remove in production)
# VITE_CONVEX_URL=http://127.0.0.1:3210
# VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_test_key

# Security Notes:
# - Never commit .env.local to version control
# - Use production keys for production deployment
# - Ensure HTTPS is enabled in production
# - Regularly rotate API keys

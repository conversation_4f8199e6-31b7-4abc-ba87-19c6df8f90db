{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"VITE_CONVEX_URL": "@convex_url", "VITE_CLERK_PUBLISHABLE_KEY": "@clerk_publishable_key", "CLERK_JWT_ISSUER_DOMAIN": "@clerk_jwt_issuer_domain"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}